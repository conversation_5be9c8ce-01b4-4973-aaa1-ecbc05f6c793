import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/contact.dart';
import '../models/call_record.dart';
import 'contact_service.dart';

class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  static const String _callHistoryKey = 'call_history';
  final ContactService _contactService = ContactService();
  
  List<CallRecord> _callHistory = [];
  bool _isInitialized = false;

  List<CallRecord> get callHistory => List.unmodifiable(_callHistory);
  List<CallRecord> get recentCalls => _callHistory.take(20).toList();

  /// Initialise le service d'appels
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCallHistory();
      _isInitialized = true;
      
      if (kDebugMode) {
        print('CallService: Initialisé avec ${_callHistory.length} appels');
      }
    } catch (e) {
      if (kDebugMode) {
        print('CallService: Erreur initialisation: $e');
      }
    }
  }

  /// Charge l'historique des appels
  Future<void> _loadCallHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_callHistoryKey);
      
      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        _callHistory = historyList.map((json) => CallRecord.fromJson(json)).toList();
        
        // Trier par date décroissante
        _callHistory.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        
        if (kDebugMode) {
          print('CallService: ${_callHistory.length} appels chargés');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('CallService: Erreur chargement historique: $e');
      }
    }
  }

  /// Sauvegarde l'historique des appels
  Future<void> _saveCallHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = json.encode(_callHistory.map((c) => c.toJson()).toList());
      await prefs.setString(_callHistoryKey, historyJson);
      
      if (kDebugMode) {
        print('CallService: Historique sauvegardé (${_callHistory.length} appels)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('CallService: Erreur sauvegarde historique: $e');
      }
    }
  }

  /// Effectue un appel téléphonique
  Future<bool> makeCall(String phoneNumber, {String? contactId, String? contactName}) async {
    try {
      // Nettoyer le numéro de téléphone
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      final uri = Uri(scheme: 'tel', path: cleanNumber);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        
        // Enregistrer l'appel dans l'historique
        await _recordCall(
          phoneNumber: cleanNumber,
          contactId: contactId,
          contactName: contactName ?? phoneNumber,
          type: CallType.outgoing,
        );
        
        // Mettre à jour la date du dernier appel du contact
        if (contactId != null) {
          await _contactService.updateLastCall(contactId);
        }
        
        if (kDebugMode) {
          print('CallService: Appel initié vers $cleanNumber');
        }
        
        return true;
      } else {
        if (kDebugMode) {
          print('CallService: Impossible de lancer l\'appel vers $cleanNumber');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('CallService: Erreur lors de l\'appel: $e');
      }
      return false;
    }
  }

  /// Effectue un appel d'urgence
  Future<bool> makeEmergencyCall(String emergencyNumber, String serviceName) async {
    try {
      final success = await makeCall(
        emergencyNumber,
        contactName: serviceName,
      );
      
      if (success) {
        // Marquer comme appel d'urgence
        if (_callHistory.isNotEmpty) {
          final lastCall = _callHistory.first;
          final emergencyCall = lastCall.copyWith(isEmergency: true);
          _callHistory[0] = emergencyCall;
          await _saveCallHistory();
        }
        
        if (kDebugMode) {
          print('CallService: Appel d\'urgence initié vers $serviceName ($emergencyNumber)');
        }
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('CallService: Erreur appel d\'urgence: $e');
      }
      return false;
    }
  }

  /// Enregistre un appel dans l'historique
  Future<void> _recordCall({
    required String phoneNumber,
    String? contactId,
    required String contactName,
    required CallType type,
    bool isEmergency = false,
    Duration? duration,
  }) async {
    final callRecord = CallRecord(
      id: _generateId(),
      phoneNumber: phoneNumber,
      contactId: contactId,
      contactName: contactName,
      type: type,
      timestamp: DateTime.now(),
      duration: duration ?? Duration.zero,
      isEmergency: isEmergency,
    );
    
    _callHistory.insert(0, callRecord);
    
    // Limiter l'historique à 100 appels
    if (_callHistory.length > 100) {
      _callHistory = _callHistory.take(100).toList();
    }
    
    await _saveCallHistory();
  }

  /// Obtient l'historique des appels pour un contact
  List<CallRecord> getCallHistoryForContact(String contactId) {
    return _callHistory.where((call) => call.contactId == contactId).toList();
  }

  /// Obtient les appels d'urgence
  List<CallRecord> getEmergencyCalls() {
    return _callHistory.where((call) => call.isEmergency).toList();
  }

  /// Supprime un appel de l'historique
  Future<bool> deleteCallRecord(String callId) async {
    final index = _callHistory.indexWhere((call) => call.id == callId);
    if (index == -1) return false;
    
    _callHistory.removeAt(index);
    await _saveCallHistory();
    
    if (kDebugMode) {
      print('CallService: Appel supprimé de l\'historique');
    }
    
    return true;
  }

  /// Efface tout l'historique des appels
  Future<void> clearCallHistory() async {
    _callHistory.clear();
    await _saveCallHistory();
    
    if (kDebugMode) {
      print('CallService: Historique des appels effacé');
    }
  }

  /// Génère un ID unique
  String _generateId() {
    return 'call_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Nettoie les ressources
  Future<void> dispose() async {
    _callHistory.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('CallService: Ressources libérées');
    }
  }
}


