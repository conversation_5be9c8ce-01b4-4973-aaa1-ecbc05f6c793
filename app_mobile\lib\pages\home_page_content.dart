import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geocoding/geocoding.dart';

// GpsService
class GpsService {
  Stream<Position>? _positionStream;

  Stream<Position> get positionStream {
    _positionStream ??= Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5,
      ),
    );
    return _positionStream!;
  }

  Future<void> startTracking() async {
    // No-op, stream initializes on first subscription
  }

  void stopTracking() {
    _positionStream = null;
  }

  String formatPosition(Position position) {
    return 'Lat: ${position.latitude.toStringAsFixed(6)}, '
        'Lon: ${position.longitude.toStringAsFixed(6)}, '
        'Alt: ${position.altitude.toStringAsFixed(2)} m';
  }
}

// MqttService (Minimal Implementation)
class MqttService {
  late MqttServerClient _client;

  MqttService() {
    _client = MqttServerClient('***************', 'flutter_client_${DateTime.now().millisecondsSinceEpoch}');
    _client.port = 1883;
    _client.logging(on: false);
    _client.setProtocolV311();
    _client.keepAlivePeriod = 20;
  }

  Future<void> connect() async {
    try {
      await _client.connect();
      print('MQTT Connecteduuuyu');
    } catch (e) {
      print('MQTT Connection failed: $e');
      _client.disconnect();
    }
  }

  void publishGpsPosition({
    required double latitude,
    required double longitude,
    required double altitude,
    required double accuracy,
    required double speed,
    required double heading,
  }) {
    if (_client.connectionStatus?.state == MqttConnectionState.connected) {
      final builder = MqttClientPayloadBuilder();
      builder.addString(
        'Lat: $latitude, Lon: $longitude, Alt: $altitude, '
            'Acc: $accuracy, Spd: $speed, Hdg: $heading',
      );
      _client.publishMessage(
        'canne_connectee/gps',
        MqttQos.atLeastOnce,
        builder.payload!,
      );
    }
  }

  void disconnect() {
    _client.disconnect();
  }
}

// GpsMapWidget
class GpsMapWidget extends StatefulWidget {
  const GpsMapWidget({super.key});

  @override
  State<GpsMapWidget> createState() => _GpsMapWidgetState();
}

class _GpsMapWidgetState extends State<GpsMapWidget> {
  final MapController _mapController = MapController();
  late final GpsService _gpsService;

  List<LatLng> _trace = [];
  LatLng? _currentPosition;
  bool _shouldCenterMap = true;
  bool _isTracking = true;
  double _totalDistance = 0.0;
  String? _errorMessage;

  static const double _minDistance = 5.0;

  @override
  void initState() {
    super.initState();
    _gpsService = GpsService();
    _initializeGps();
  }

  Future<void> _initializeGps() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      setState(() => _errorMessage = 'Location services are disabled. Please enable GPS.');
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        setState(() => _errorMessage = 'Location permissions are denied.');
        return;
      }
      if (permission == LocationPermission.deniedForever) {
        setState(() => _errorMessage = 'Location permissions are permanently denied. Please enable them in settings.');
        return;
      }
    }

    try {
      await _gpsService.startTracking();
    } catch (e) {
      setState(() => _errorMessage = 'Failed to start tracking: $e');
    }
  }

  @override
  void dispose() {
    _gpsService.stopTracking();
    super.dispose();
  }

  void _goToMyPosition() {
    if (_currentPosition != null) {
      _mapController.move(_currentPosition!, 17);
    }
  }

  void _fitTraceToView() {
    if (_trace.isEmpty) return;

    if (_trace.length == 1) {
      _mapController.move(_trace.first, 17);
      return;
    }

    final bounds = LatLngBounds.fromPoints(_trace);
    _mapController.fitCamera(
      CameraFit.bounds(
        bounds: bounds,
        padding: const EdgeInsets.all(50),
      ),
    );
  }

  void _toggleTracking() {
    setState(() => _isTracking = !_isTracking);
  }

  void _clearTrace() {
    setState(() {
      _trace.clear();
      _totalDistance = 0.0;
    });
  }

  double _calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    );
  }

  void _addPointToTrace(LatLng newPoint) {
    if (!_isTracking) return;

    if (_trace.isEmpty) {
      _trace.add(newPoint);
      return;
    }

    final lastPoint = _trace.last;
    final distance = _calculateDistance(lastPoint, newPoint);

    if (distance >= _minDistance) {
      setState(() {
        _trace.add(newPoint);
        _totalDistance += distance;
      });
    }
  }

  String _formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.toStringAsFixed(0)} m';
    }
    return '${(distanceInMeters / 1000).toStringAsFixed(2)} km';
  }


  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (_errorMessage != null)
          Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white70,
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red, fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
          )
        else
          StreamBuilder<Position>(
            stream: _gpsService.positionStream,
            builder: (context, snapshot) {
              if (!snapshot.hasData) {
                return const Center(child: CircularProgressIndicator());
              }

              final position = snapshot.data!;
              final current = LatLng(position.latitude, position.longitude);
              _currentPosition = current;

              _addPointToTrace(current);

              if (_shouldCenterMap) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _mapController.move(current, _mapController.camera.zoom);
                });
              }

              return FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                  initialCenter: current,
                  initialZoom: 17,
                  onPositionChanged: (_, __) => _shouldCenterMap = false,
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    subdomains: const ['a', 'b', 'c'],
                  ),
                  if (_trace.length > 1)
                    PolylineLayer(
                      polylines: [
                        Polyline(
                          points: _trace,
                          strokeWidth: 4.0,
                          color: _isTracking ? Colors.blueAccent : Colors.grey,
                        ),
                      ],
                    ),
                  MarkerLayer(
                    markers: [
                      Marker(
                        width: 60,
                        height: 60,
                        point: current,
                        child: Container(
                          decoration: BoxDecoration(
                            color: _isTracking ? Colors.blue : Colors.grey,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            _isTracking ? Icons.my_location : Icons.location_disabled,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                      if (_trace.isNotEmpty)
                        Marker(
                          width: 40,
                          height: 40,
                          point: _trace.first,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              );
            },
          ),
        Positioned(
          top: 50,
          left: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Distance',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _formatDistance(_totalDistance),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueAccent,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Points',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${_trace.length}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blueAccent,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Statut',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _isTracking ? 'Actif' : 'Pausé',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isTracking ? Colors.green : Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton(
                heroTag: "fit_trace",
                onPressed: _trace.length > 1 ? _fitTraceToView : null,
                backgroundColor: _trace.length > 1 ? Colors.purple : Colors.grey,
                mini: true,
                child: const Icon(Icons.fit_screen, color: Colors.white),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: "clear",
                onPressed: _clearTrace,
                backgroundColor: Colors.red,
                mini: true,
                child: const Icon(Icons.clear, color: Colors.white),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: "tracking",
                onPressed: _toggleTracking,
                backgroundColor: _isTracking ? Colors.orange : Colors.green,
                mini: true,
                child: Icon(
                  _isTracking ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: "center",
                onPressed: () {
                  setState(() => _shouldCenterMap = true);
                  _goToMyPosition();
                },
                backgroundColor: Colors.blue,
                child: const Icon(Icons.gps_fixed, color: Colors.white),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// HomeContent
class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent> {
  final GpsService _gpsService = GpsService();
  final MqttService _mqttService = MqttService();
  String _positionText = 'En attente de position...';

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  Future<void> _initServices() async {
    await _mqttService.connect();
    await _gpsService.startTracking();

    _gpsService.positionStream.listen((position) {
      _mqttService.publishGpsPosition(
        latitude: position.latitude,
        longitude: position.longitude,
        altitude: position.altitude,
        accuracy: position.accuracy,
        speed: position.speed,
        heading: position.heading,
      );

      _updateAddress(position); // 🟢 C’est ici qu’on affiche le nom du lieu
    });

  }

  Future<void> _updateAddress(Position position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        final address = '${place.street}, ${place.locality}, ${place.country}';

        setState(() {
          _positionText = address;
        });
      } else {
        setState(() {
          _positionText = 'Adresse non trouvée';
        });
      }
    } catch (e) {
      setState(() {
        _positionText = 'Erreur lors de la récupération de l\'adresse';
      });
      print('Erreur de géocodage : $e');
    }
  }

  @override
  void dispose() {
    _gpsService.stopTracking();
    _mqttService.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 18.0),
        child: Column(
          children: [
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF7900),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(16)),
                        child: GpsMapWidget(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    'Votre Position Actuelle',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _positionText.isEmpty ? 'Chargement de la position...' : _positionText,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  )


                ],
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              flex: 2,
              child: ListView(
                children: [
                  _buildListItem(
                    title: 'Obstacle détecté',
                    subtitle: 'Véhicule mal garé détecté à 5m',
                    icon: Icons.warning,
                    color: Colors.orange,
                  ),
                  _buildListItem(
                    title: 'Appel reçu',
                    subtitle: 'Appel d’urgence reçu à 14h10',
                    icon: Icons.phone,
                    color: Colors.redAccent,
                  ),
                  _buildListItem(
                    title: 'Trajet terminé',
                    subtitle: 'Trajet jusqu’au centre de santé complété',
                    icon: Icons.check_circle,
                    color: Colors.green,
                  ),
                  _buildListItem(
                    title: 'Message vocal',
                    subtitle: 'Message reçu de votre médecin',
                    icon: Icons.mic,
                    color: Colors.blueAccent,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Main App
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Canne Connectée',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const Scaffold(
        body: HomeContent(),
      ),
    );
  }
}