import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

class GpsService extends ChangeNotifier {
  static final GpsService _instance = GpsService._internal();
  factory GpsService() => _instance;
  GpsService._internal();

  Position? _currentPosition;
  bool _isTracking = false;
  String _locationStatus = 'Arrêté';
  StreamSubscription<Position>? _positionStreamSubscription;

  // Stream pour les mises à jour de position
  final StreamController<Position> _positionStreamController =
  StreamController<Position>.broadcast();

  // Getters
  Position? get currentPosition => _currentPosition;
  bool get isTracking => _isTracking;
  String get locationStatus => _locationStatus;
  Stream<Position> get positionStream => _positionStreamController.stream;

  /// Vérifie et demande les permissions de localisation
  Future<bool> checkAndRequestPermissions() async {
    try {
      // Vérifier le statut du service de localisation
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _updateLocationStatus('Service de localisation désactivé');
        return false;
      }

      // Vérifier les permissions
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _updateLocationStatus('Permission de localisation refusée');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _updateLocationStatus('Permission de localisation refusée définitivement');
        return false;
      }

      _updateLocationStatus('Permissions accordées');
      return true;
    } catch (e) {
      _updateLocationStatus('Erreur permissions: $e');
      debugPrint('Erreur permissions GPS: $e');
      return false;
    }
  }

  /// Obtient la position actuelle une seule fois
  Future<Position?> getCurrentPosition() async {
    try {
      if (!await checkAndRequestPermissions()) {
        return null;
      }

      _updateLocationStatus('Obtention de la position...');

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );

      _currentPosition = position;
      _positionStreamController.add(position);
      _updateLocationStatus('Position obtenue');

      return position;
    } catch (e) {
      _updateLocationStatus('Erreur: $e');
      debugPrint('Erreur getCurrentPosition: $e');
      return null;
    }
  }

  /// Démarre le suivi GPS en temps réel
  Future<bool> startTracking({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 5, // mètres
    int timeInterval = 5000, // millisecondes
  }) async {
    try {
      if (_isTracking) {
        debugPrint('Le suivi GPS est déjà actif');
        return true;
      }

      if (!await checkAndRequestPermissions()) {
        return false;
      }

      _updateLocationStatus('Démarrage du suivi GPS...');

      // Configuration des paramètres de localisation
      LocationSettings locationSettings = LocationSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
        timeLimit: Duration(milliseconds: timeInterval),
      );

      // Démarrer l'écoute des positions
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
            (Position position) {
          _currentPosition = position;
          _positionStreamController.add(position);
          _updateLocationStatus('Suivi actif - Précision: ${position.accuracy.toStringAsFixed(1)}m');

          debugPrint('Position mise à jour: ${position.latitude}, ${position.longitude}');
        },
        onError: (error) {
          _updateLocationStatus('Erreur suivi: $error');
          debugPrint('Erreur stream GPS: $error');
        },
      );

      _isTracking = true;
      return true;
    } catch (e) {
      _updateLocationStatus('Erreur démarrage: $e');
      debugPrint('Erreur startTracking: $e');
      return false;
    }
  }

  /// Arrête le suivi GPS
  Future<void> stopTracking() async {
    try {
      if (_positionStreamSubscription != null) {
        await _positionStreamSubscription!.cancel();
        _positionStreamSubscription = null;
      }

      _isTracking = false;
      _updateLocationStatus('Suivi arrêté');
      debugPrint('Suivi GPS arrêté');
    } catch (e) {
      debugPrint('Erreur stopTracking: $e');
    }
  }

  /// Calcule la distance entre deux points
  double calculateDistance(
      double startLatitude,
      double startLongitude,
      double endLatitude,
      double endLongitude,
      ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Calcule le bearing (direction) entre deux points
  double calculateBearing(
      double startLatitude,
      double startLongitude,
      double endLatitude,
      double endLongitude,
      ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Vérifie si une position est dans un rayon donné
  bool isWithinRadius(
      double centerLatitude,
      double centerLongitude,
      double targetLatitude,
      double targetLongitude,
      double radiusInMeters,
      ) {
    double distance = calculateDistance(
      centerLatitude,
      centerLongitude,
      targetLatitude,
      targetLongitude,
    );
    return distance <= radiusInMeters;
  }

  /// Obtient des informations détaillées sur la position
  Map<String, dynamic> getPositionInfo(Position position) {
    return {
      'latitude': position.latitude,
      'longitude': position.longitude,
      'altitude': position.altitude,
      'accuracy': position.accuracy,
      'speed': position.speed,
      'speedAccuracy': position.speedAccuracy,
      'heading': position.heading,
      'headingAccuracy': position.headingAccuracy,
      'timestamp': position.timestamp?.toIso8601String(),
      'isMocked': position.isMocked,
    };
  }

  /// Formate la position pour l'affichage
  String formatPosition(Position position) {
    return 'Lat: ${position.latitude.toStringAsFixed(6)}, '
        'Lng: ${position.longitude.toStringAsFixed(6)}, '
        'Précision: ${position.accuracy.toStringAsFixed(1)}m';
  }

  /// Formate la vitesse
  String formatSpeed(double speedMps) {
    double speedKmh = speedMps * 3.6;
    return '${speedKmh.toStringAsFixed(1)} km/h';
  }

  /// Formate la direction
  String formatHeading(double heading) {
    List<String> directions = [
      'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
      'S', 'SSO', 'SO', 'OSO', 'O', 'ONO', 'NO', 'NNO'
    ];

    int index = ((heading + 11.25) / 22.5).floor() % 16;
    return '${directions[index]} (${heading.toStringAsFixed(0)}°)';
  }

  void _updateLocationStatus(String status) {
    _locationStatus = status;
    notifyListeners();
    debugPrint('GPS Status: $status');
  }

  @override
  void dispose() {
    stopTracking();
    _positionStreamController.close();
    super.dispose();
  }
}
