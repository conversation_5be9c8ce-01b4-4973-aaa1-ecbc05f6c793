import 'package:flutter/material.dart';
import '../../models/proche.dart';
import '../../services/proche_service.dart';

class AddProchePage extends StatefulWidget {
  const AddProchePage({super.key});

  @override
  State<AddProchePage> createState() => _AddProchePageState();
}

class _AddProchePageState extends State<AddProchePage> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final ProcheService _procheService = ProcheService();
  
  RelationType _selectedRelationType = RelationType.famille;
  ProchePriority _selectedPriority = ProchePriority.normale;
  bool _canReceiveAlerts = true;
  bool _canTrackLocation = true;
  bool _canReceiveCalls = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _emailController.dispose();
    _telephoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text(
          'Inviter un proche',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header info
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Votre proche recevra une invitation par email pour rejoindre votre réseau de soutien.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Informations personnelles
              const Text(
                'Informations personnelles',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Nom
              _buildTextField(
                label: 'Nom',
                controller: _nomController,
                hint: 'Nom de famille',
                icon: Icons.person_outline,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer le nom';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Prénom
              _buildTextField(
                label: 'Prénom',
                controller: _prenomController,
                hint: 'Prénom',
                icon: Icons.person_outline,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer le prénom';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Email
              _buildTextField(
                label: 'E-mail',
                controller: _emailController,
                hint: '<EMAIL>',
                icon: Icons.email_outlined,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer l\'e-mail';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Veuillez entrer un e-mail valide';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Téléphone
              _buildTextField(
                label: 'Téléphone (optionnel)',
                controller: _telephoneController,
                hint: '+33 6 12 34 56 78',
                icon: Icons.phone_outlined,
                keyboardType: TextInputType.phone,
                isRequired: false,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^\+?[0-9\s\-\(\)]{8,20}$').hasMatch(value)) {
                      return 'Veuillez entrer un numéro valide';
                    }
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 32),
              
              // Type de relation
              const Text(
                'Type de relation',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  children: RelationType.values.map((type) {
                    return RadioListTile<RelationType>(
                      title: Row(
                        children: [
                          Text(
                            type.icon,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            type.displayName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      value: type,
                      groupValue: _selectedRelationType,
                      activeColor: const Color(0xFFFF7900),
                      onChanged: (RelationType? value) {
                        setState(() {
                          _selectedRelationType = value!;
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Priorité
              const Text(
                'Niveau de priorité',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonFormField<ProchePriority>(
                  value: _selectedPriority,
                  decoration: const InputDecoration(
                    labelText: 'Priorité',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.priority_high),
                  ),
                  items: ProchePriority.values.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: _getPriorityColor(priority),
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(priority.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (ProchePriority? value) {
                    setState(() {
                      _selectedPriority = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Permissions
              const Text(
                'Permissions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  children: [
                    _buildPermissionTile(
                      title: 'Recevoir les alertes',
                      subtitle: 'Notifications en cas d\'urgence',
                      icon: Icons.notifications_active,
                      value: _canReceiveAlerts,
                      onChanged: (value) {
                        setState(() {
                          _canReceiveAlerts = value;
                        });
                      },
                    ),
                    const Divider(),
                    _buildPermissionTile(
                      title: 'Suivre ma localisation',
                      subtitle: 'Voir ma position en temps réel',
                      icon: Icons.location_on,
                      value: _canTrackLocation,
                      onChanged: (value) {
                        setState(() {
                          _canTrackLocation = value;
                        });
                      },
                    ),
                    const Divider(),
                    _buildPermissionTile(
                      title: 'Recevoir mes appels',
                      subtitle: 'Être contacté en cas de besoin',
                      icon: Icons.phone,
                      value: _canReceiveCalls,
                      onChanged: (value) {
                        setState(() {
                          _canReceiveCalls = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Color(0xFFFF7900)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Annuler',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFF7900),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _handleInviteProche,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFF7900),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'Envoyer l\'invitation',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool isRequired = true,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            if (!isRequired)
              const Text(
                ' (optionnel)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            filled: true,
            fillColor: Colors.grey[100],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFFF7900)),
            ),
            prefixIcon: Icon(icon, color: Colors.grey),
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildPermissionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.grey,
        ),
      ),
      secondary: Icon(
        icon,
        color: const Color(0xFFFF7900),
      ),
      value: value,
      activeColor: const Color(0xFFFF7900),
      onChanged: onChanged,
    );
  }

  Color _getPriorityColor(ProchePriority priority) {
    switch (priority) {
      case ProchePriority.urgence:
        return Colors.red;
      case ProchePriority.haute:
        return Colors.orange;
      case ProchePriority.normale:
        return Colors.blue;
      case ProchePriority.faible:
        return Colors.green;
    }
  }

  Future<void> _handleInviteProche() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _procheService.inviterProche(
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        email: _emailController.text.trim(),
        telephone: _telephoneController.text.trim().isEmpty 
            ? null 
            : _telephoneController.text.trim(),
        relationType: _selectedRelationType,
        priority: _selectedPriority,
        canReceiveAlerts: _canReceiveAlerts,
        canTrackLocation: _canTrackLocation,
        canReceiveCalls: _canReceiveCalls,
      );

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true); // Retourner true pour indiquer le succès
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
