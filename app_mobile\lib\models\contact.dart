class Contact {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final ContactType type;
  final String? avatar;
  final bool isFavorite;
  final bool isEmergency;
  final DateTime? lastCallDate;
  final DateTime? lastMessageDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  Contact({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.type,
    this.avatar,
    this.isFavorite = false,
    this.isEmergency = false,
    this.lastCallDate,
    this.lastMessageDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Contact.fromJson(Map<String, dynamic> json) {
    return Contact(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      type: ContactType.values.firstWhere(
        (e) => e.toString() == 'ContactType.${json['type']}',
        orElse: () => ContactType.personal,
      ),
      avatar: json['avatar'],
      isFavorite: json['is_favorite'] ?? false,
      isEmergency: json['is_emergency'] ?? false,
      lastCallDate: json['last_call_date'] != null 
          ? DateTime.parse(json['last_call_date']) 
          : null,
      lastMessageDate: json['last_message_date'] != null 
          ? DateTime.parse(json['last_message_date']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'type': type.toString().split('.').last,
      'avatar': avatar,
      'is_favorite': isFavorite,
      'is_emergency': isEmergency,
      'last_call_date': lastCallDate?.toIso8601String(),
      'last_message_date': lastMessageDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Contact copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    ContactType? type,
    String? avatar,
    bool? isFavorite,
    bool? isEmergency,
    DateTime? lastCallDate,
    DateTime? lastMessageDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Contact(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      type: type ?? this.type,
      avatar: avatar ?? this.avatar,
      isFavorite: isFavorite ?? this.isFavorite,
      isEmergency: isEmergency ?? this.isEmergency,
      lastCallDate: lastCallDate ?? this.lastCallDate,
      lastMessageDate: lastMessageDate ?? this.lastMessageDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => name.isNotEmpty ? name : phone;
  
  String get formattedPhone {
    // Format ivoirien : +225 XX XX XX XX XX
    if (phone.startsWith('+225')) {
      final digits = phone.substring(4).replaceAll(' ', '');
      if (digits.length >= 10) {
        return '+225 ${digits.substring(0, 2)} ${digits.substring(2, 4)} ${digits.substring(4, 6)} ${digits.substring(6, 8)} ${digits.substring(8, 10)}';
      }
    }
    return phone;
  }

  String get lastActivityText {
    if (lastCallDate != null && lastMessageDate != null) {
      final latest = lastCallDate!.isAfter(lastMessageDate!) ? lastCallDate! : lastMessageDate!;
      return _formatRelativeTime(latest);
    } else if (lastCallDate != null) {
      return 'Appel: ${_formatRelativeTime(lastCallDate!)}';
    } else if (lastMessageDate != null) {
      return 'Message: ${_formatRelativeTime(lastMessageDate!)}';
    }
    return 'Aucune activité';
  }

  String _formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inMinutes < 60) {
      return 'Il y a ${difference.inMinutes}min';
    } else if (difference.inHours < 24) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'Il y a $weeks semaine${weeks > 1 ? 's' : ''}';
    } else {
      final months = (difference.inDays / 30).floor();
      return 'Il y a $months mois';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Contact && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Contact{id: $id, name: $name, phone: $phone, type: $type}';
  }
}

enum ContactType {
  personal,
  family,
  medical,
  emergency,
  service,
  pharmacy,
  transport,
  other,
}

extension ContactTypeExtension on ContactType {
  String get displayName {
    switch (this) {
      case ContactType.personal:
        return 'Personnel';
      case ContactType.family:
        return 'Famille';
      case ContactType.medical:
        return 'Médical';
      case ContactType.emergency:
        return 'Urgence';
      case ContactType.service:
        return 'Service';
      case ContactType.pharmacy:
        return 'Pharmacie';
      case ContactType.transport:
        return 'Transport';
      case ContactType.other:
        return 'Autre';
    }
  }

  String get icon {
    switch (this) {
      case ContactType.personal:
        return '👤';
      case ContactType.family:
        return '👨‍👩‍👧‍👦';
      case ContactType.medical:
        return '🏥';
      case ContactType.emergency:
        return '🚨';
      case ContactType.service:
        return '🛠️';
      case ContactType.pharmacy:
        return '💊';
      case ContactType.transport:
        return '🚗';
      case ContactType.other:
        return '📞';
    }
  }
}
