enum UserType {
  proche,
  aveugle,
}

extension UserTypeExtension on UserType {
  String get displayName {
    switch (this) {
      case UserType.proche:
        return 'Proche/Aidant';
      case UserType.aveugle:
        return 'Personne malvoyante';
    }
  }

  String get description {
    switch (this) {
      case UserType.proche:
        return 'Je suis un proche, un aidant ou un accompagnateur';
      case UserType.aveugle:
        return 'Je suis une personne malvoyante ou aveugle';
    }
  }

  String get icon {
    switch (this) {
      case UserType.proche:
        return '👥';
      case UserType.aveugle:
        return '🦯';
    }
  }
}

class User {
  final String id;
  final String nomUtilisateur;
  final String prenomUtilisateur;
  final String emailUtilisateur;
  final String? contactUtilisateur;
  final String? adresseUtilisateur;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  User({
    required this.id,
    required this.nomUtilisateur,
    required this.prenomUtilisateur,
    required this.emailUtilisateur,
    this.contactUtilisateur,
    this.adresseUtilisateur,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      nomUtilisateur: json['nom_utilisateur'] ?? '',
      prenomUtilisateur: json['prenom_utilisateur'] ?? '',
      emailUtilisateur: json['email_utilisateur'] ?? '',
      contactUtilisateur: json['contact_utilisateur']?.toString(),
      adresseUtilisateur: json['adresse_utilisateur']?.toString(),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nom_utilisateur': nomUtilisateur,
      'prenom_utilisateur': prenomUtilisateur,
      'email_utilisateur': emailUtilisateur,
      'contact_utilisateur': contactUtilisateur,
      'adresse_utilisateur': adresseUtilisateur,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  User copyWith({
    String? id,
    String? nomUtilisateur,
    String? prenomUtilisateur,
    String? emailUtilisateur,
    String? contactUtilisateur,
    String? adresseUtilisateur,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      nomUtilisateur: nomUtilisateur ?? this.nomUtilisateur,
      prenomUtilisateur: prenomUtilisateur ?? this.prenomUtilisateur,
      emailUtilisateur: emailUtilisateur ?? this.emailUtilisateur,
      contactUtilisateur: contactUtilisateur ?? this.contactUtilisateur,
      adresseUtilisateur: adresseUtilisateur ?? this.adresseUtilisateur,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  String get fullName => '$prenomUtilisateur $nomUtilisateur';

  @override
  String toString() {
    return 'User{id: $id, fullName: $fullName, email: $emailUtilisateur}';
  }
}
