import 'dart:convert';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:flutter/foundation.dart';

/// Service MQTT pour la communication avec le serveur
class MqttService {
  late MqttServerClient _client;
  bool _isConnected = false;

  // Topics MQTT pour la canne connectée
  static const String _baseTopicPrefix = 'canne_connectee';
  static const String _gpsTopicSuffix = 'gps';
  static const String _sosTopicSuffix = 'sos';
  static const String _healthTopicSuffix = 'health';
  static const String _batteryTopicSuffix = 'battery';
  static const String _obstacleTopicSuffix = 'obstacle';
  static const String _fallTopicSuffix = 'fall';
  static const String _heartRateTopicSuffix = 'heartrate';
  static const String _stepsTopicSuffix = 'steps';
  static const String _journeyTopicSuffix = 'journey';
  static const String _alertTopicSuffix = 'alert';
  static const String _statusTopicSuffix = 'status';
  static const String _commandTopicSuffix = 'command';

  MqttService() {
    _client = MqttServerClient(
      '192.168.252.246',
      'flutter_client_${DateTime.now().millisecondsSinceEpoch}',
    );
    _client.port = 1883;
    _client.logging(on: false);
    _client.setProtocolV311();
    _client.keepAlivePeriod = 20;
    _client.autoReconnect = true;
  }

  bool get isConnected => _isConnected;

  Future<void> connect() async {
    try {
      await _client.connect();
      _isConnected = _client.connectionStatus?.state == MqttConnectionState.connected;

      if (_isConnected) {
        if (kDebugMode) {
          print('MQTT Connected successfully');
        }

        // S'abonner aux topics de commandes
        _subscribeToCommandTopics();

        // Publier le statut de connexion
        publishDeviceStatus('online');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MQTT Connection failed: $e');
      }
      _isConnected = false;
      _client.disconnect();
    }
  }

  void _subscribeToCommandTopics() {
    if (_isConnected) {
      _client.subscribe('$_baseTopicPrefix/$_commandTopicSuffix', MqttQos.atLeastOnce);
      if (kDebugMode) {
        print('Subscribed to command topic');
      }
    }
  }

  /// Publier la position GPS
  void publishGpsPosition({
    required double latitude,
    required double longitude,
    required double altitude,
    required double accuracy,
    required double speed,
    required double heading,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'latitude': latitude,
      'longitude': longitude,
      'altitude': altitude,
      'accuracy': accuracy,
      'speed': speed,
      'heading': heading,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_gpsTopicSuffix', data);
  }

  /// Publier une alerte SOS
  void publishSOSAlert({
    required double latitude,
    required double longitude,
    String? message,
    String? audioPath,
  }) {
    if (!_isConnected) {
      if (kDebugMode) {
        print('❌ Impossible d\'envoyer SOS : client MQTT non connecté');
      }
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    final googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';

    final data = {
      'timestamp': timestamp,
      'latitude': latitude,
      'longitude': longitude,
      'message': message ?? 'Alerte SOS déclenchée',
      'maps_url': googleMapsUrl,
      'device_id': _client.clientIdentifier,
      'priority': 'CRITICAL',
      'type': 'SOS',
      'audio_path': audioPath,
    };

    _publishJson('$_baseTopicPrefix/$_sosTopicSuffix', data);

    if (kDebugMode) {
      print('✅ Alerte SOS envoyée avec position exacte : $googleMapsUrl');
    }
  }

  /// Publier les données de santé
  void publishHealthData({
    required int heartRate,
    required int steps,
    required double temperature,
    String? bloodPressure,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'heart_rate': heartRate,
      'steps': steps,
      'temperature': temperature,
      'blood_pressure': bloodPressure,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_healthTopicSuffix', data);
  }

  /// Publier le niveau de batterie
  void publishBatteryLevel({
    required int batteryLevel,
    required bool isCharging,
    int? estimatedHours,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'battery_level': batteryLevel,
      'is_charging': isCharging,
      'estimated_hours': estimatedHours,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_batteryTopicSuffix', data);
  }

  /// Publier une détection d'obstacle
  void publishObstacleDetection({
    required String obstacleType,
    required double distance,
    required double latitude,
    required double longitude,
    String? description,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'obstacle_type': obstacleType,
      'distance': distance,
      'latitude': latitude,
      'longitude': longitude,
      'description': description,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_obstacleTopicSuffix', data);
  }

  /// Publier une détection de chute
  void publishFallDetection({
    required double latitude,
    required double longitude,
    required double accelerationMagnitude,
    String? severity,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'latitude': latitude,
      'longitude': longitude,
      'acceleration': accelerationMagnitude,
      'severity': severity ?? 'moderate',
      'device_id': _client.clientIdentifier,
      'type': 'FALL_DETECTED',
      'priority': 'HIGH',
    };

    _publishJson('$_baseTopicPrefix/$_fallTopicSuffix', data);
  }

  /// Publier les données de trajet
  void publishJourneyData({
    required String journeyId,
    required String action, // 'start', 'update', 'end'
    required double latitude,
    required double longitude,
    double? distance,
    int? duration,
    String? startLocation,
    String? endLocation,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'journey_id': journeyId,
      'action': action,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
      'duration': duration,
      'start_location': startLocation,
      'end_location': endLocation,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_journeyTopicSuffix', data);
  }

  /// Publier une alerte générale
  void publishAlert({
    required String alertType,
    required String message,
    required String priority, // 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    double? latitude,
    double? longitude,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'alert_type': alertType,
      'message': message,
      'priority': priority,
      'latitude': latitude,
      'longitude': longitude,
      'device_id': _client.clientIdentifier,
      ...?additionalData,
    };

    _publishJson('$_baseTopicPrefix/$_alertTopicSuffix', data);
  }

  /// Publier le statut de l'appareil
  void publishDeviceStatus(String status) {
    if (!_isConnected && status != 'offline') return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'status': status, // 'online', 'offline', 'maintenance', 'low_battery'
      'device_id': _client.clientIdentifier,
      'app_version': '1.0.0',
    };

    _publishJson('$_baseTopicPrefix/$_statusTopicSuffix', data);
  }

  /// Publier les pas comptés
  void publishStepsCount({
    required int totalSteps,
    required int dailySteps,
    required double distance,
    required int calories,
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'total_steps': totalSteps,
      'daily_steps': dailySteps,
      'distance': distance,
      'calories': calories,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_stepsTopicSuffix', data);
  }

  /// Publier la fréquence cardiaque
  void publishHeartRate({
    required int heartRate,
    required String context, // 'rest', 'walking', 'exercise', 'stress'
  }) {
    if (!_isConnected) return;

    final data = {
      'timestamp': DateTime.now().toIso8601String(),
      'heart_rate': heartRate,
      'context': context,
      'device_id': _client.clientIdentifier,
    };

    _publishJson('$_baseTopicPrefix/$_heartRateTopicSuffix', data);
  }

  /// Méthode utilitaire pour publier des données JSON
  void _publishJson(String topic, Map<String, dynamic> data) {
    if (!_isConnected) return;

    try {
      final builder = MqttClientPayloadBuilder();
      builder.addString(json.encode(data));

      _client.publishMessage(
        topic,
        MqttQos.atLeastOnce,
        builder.payload!,
      );

      if (kDebugMode) {
        print('📤 MQTT Published to $topic: ${data['timestamp']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ MQTT Publish error: $e');
      }
    }
  }

  /// Méthode utilitaire pour publier du texte simple
  void _publishText(String topic, String message) {
    if (!_isConnected) return;

    try {
      final builder = MqttClientPayloadBuilder();
      builder.addString(message);

      _client.publishMessage(
        topic,
        MqttQos.atLeastOnce,
        builder.payload!,
      );

      if (kDebugMode) {
        print('📤 MQTT Published text to $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ MQTT Publish error: $e');
      }
    }
  }

  /// Obtenir la liste de tous les topics disponibles
  List<String> getAvailableTopics() {
    return [
      '$_baseTopicPrefix/$_gpsTopicSuffix',
      '$_baseTopicPrefix/$_sosTopicSuffix',
      '$_baseTopicPrefix/$_healthTopicSuffix',
      '$_baseTopicPrefix/$_batteryTopicSuffix',
      '$_baseTopicPrefix/$_obstacleTopicSuffix',
      '$_baseTopicPrefix/$_fallTopicSuffix',
      '$_baseTopicPrefix/$_heartRateTopicSuffix',
      '$_baseTopicPrefix/$_stepsTopicSuffix',
      '$_baseTopicPrefix/$_journeyTopicSuffix',
      '$_baseTopicPrefix/$_alertTopicSuffix',
      '$_baseTopicPrefix/$_statusTopicSuffix',
      '$_baseTopicPrefix/$_commandTopicSuffix',
    ];
  }

  /// Déconnecter le client MQTT
  void disconnect() {
    if (_isConnected) {
      // Publier le statut offline avant de se déconnecter
      publishDeviceStatus('offline');

      _client.disconnect();
      _isConnected = false;

      if (kDebugMode) {
        print('MQTT Disconnected');
      }
    }
  }
}

