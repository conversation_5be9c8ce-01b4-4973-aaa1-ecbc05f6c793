import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class NotificationService extends ChangeNotifier {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final List<NotificationItem> _notifications = [];
  int _unreadCount = 0;

  List<NotificationItem> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _unreadCount;

  void initialize() {
    _loadInitialNotifications();
    if (kDebugMode) {
      print('NotificationService: Initialisé avec ${_notifications.length} notifications');
    }
  }

  void addNotification(NotificationItem notification) {
    _notifications.insert(0, notification);
    if (!notification.isRead) {
      _unreadCount++;
    }
    notifyListeners();
    
    if (kDebugMode) {
      print('NotificationService: Nouvelle notification ajoutée - ${notification.title}');
    }
  }

  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index].isRead = true;
      _unreadCount--;
      notifyListeners();
    }
  }

  void markAllAsRead() {
    for (var notification in _notifications) {
      notification.isRead = true;
    }
    _unreadCount = 0;
    notifyListeners();
  }

  void clearAll() {
    _notifications.clear();
    _unreadCount = 0;
    notifyListeners();
  }

  void addMissedCallNotification(String callerName, String phoneNumber) {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.missedCall,
      title: 'Appel manqué',
      message: '$callerName a essayé de vous appeler',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: Icons.phone_missed,
      color: Colors.red,
      data: {'caller': callerName, 'phone': phoneNumber},
    );
    addNotification(notification);
  }

  void addMessageNotification(String senderName, String messagePreview) {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.message,
      title: 'Nouveau message',
      message: '$senderName: "$messagePreview"',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: Icons.message,
      color: Colors.blue,
      data: {'sender': senderName, 'preview': messagePreview},
    );
    addNotification(notification);
  }

  void addSOSNotification() {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.action,
      title: 'SOS déclenché',
      message: 'Alerte SOS envoyée à vos proches',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: Icons.warning,
      color: Colors.orange,
      data: {'action': 'sos_triggered'},
    );
    addNotification(notification);
  }

  void addNavigationNotification(String destination) {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.action,
      title: 'Navigation terminée',
      message: 'Vous êtes arrivé à destination: $destination',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: Icons.navigation,
      color: const Color(0xFFFF7900),
      data: {'destination': destination},
    );
    addNotification(notification);
  }

  void addCanneConnectionNotification(bool connected) {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.system,
      title: connected ? 'Canne connectée' : 'Canne déconnectée',
      message: connected 
          ? 'Votre canne a été connectée avec succès'
          : 'Votre canne a été déconnectée',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: connected ? Icons.bluetooth_connected : Icons.bluetooth_disabled,
      color: connected ? Colors.green : Colors.red,
      data: {'connected': connected},
    );
    addNotification(notification);
  }

  void addBatteryNotification(int batteryLevel) {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.system,
      title: 'Batterie faible',
      message: 'La batterie de votre canne est à $batteryLevel%',
      timeAgo: 'À l\'instant',
      isRead: false,
      icon: Icons.battery_alert,
      color: Colors.red,
      data: {'battery_level': batteryLevel},
    );
    addNotification(notification);
  }

  void _loadInitialNotifications() {
    // Charger quelques notifications d'exemple
    _notifications.addAll([
      NotificationItem(
        id: '1',
        type: NotificationType.missedCall,
        title: 'Appel manqué',
        message: 'Amian Olivier a essayé de vous appeler',
        timeAgo: 'Il y a 5 min',
        isRead: false,
        icon: Icons.phone_missed,
        color: Colors.red,
      ),
      NotificationItem(
        id: '2',
        type: NotificationType.message,
        title: 'Nouveau message',
        message: 'Maman: "Comment ça va mon chéri ?"',
        timeAgo: 'Il y a 15 min',
        isRead: false,
        icon: Icons.message,
        color: Colors.blue,
      ),
    ]);

    _unreadCount = _notifications.where((n) => !n.isRead).length;
  }

  List<NotificationItem> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  List<NotificationItem> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }
}

enum NotificationType {
  missedCall,
  message,
  action,
  system,
}

class NotificationItem {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final String timeAgo;
  bool isRead;
  final IconData icon;
  final Color color;
  final Map<String, dynamic>? data;

  NotificationItem({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.timeAgo,
    required this.isRead,
    required this.icon,
    required this.color,
    this.data,
  });
}
