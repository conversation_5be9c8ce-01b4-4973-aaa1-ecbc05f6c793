import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/contact.dart';

class ContactService {
  static final ContactService _instance = ContactService._internal();
  factory ContactService() => _instance;
  ContactService._internal();

  static const String _contactsKey = 'contacts';
  List<Contact> _contacts = [];
  bool _isInitialized = false;

  List<Contact> get contacts => List.unmodifiable(_contacts);
  List<Contact> get emergencyContacts => _contacts.where((c) => c.isEmergency).toList();
  List<Contact> get favoriteContacts => _contacts.where((c) => c.isFavorite).toList();
  List<Contact> get recentContacts {
    final recent = _contacts.where((c) => 
      c.lastCallDate != null || c.lastMessageDate != null
    ).toList();
    recent.sort((a, b) {
      final aDate = _getLatestActivity(a);
      final bDate = _getLatestActivity(b);
      return bDate.compareTo(aDate);
    });
    return recent.take(10).toList();
  }

  DateTime _getLatestActivity(Contact contact) {
    if (contact.lastCallDate != null && contact.lastMessageDate != null) {
      return contact.lastCallDate!.isAfter(contact.lastMessageDate!) 
          ? contact.lastCallDate! 
          : contact.lastMessageDate!;
    } else if (contact.lastCallDate != null) {
      return contact.lastCallDate!;
    } else if (contact.lastMessageDate != null) {
      return contact.lastMessageDate!;
    }
    return contact.createdAt;
  }

  /// Initialise le service avec des contacts par défaut
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadContacts();
      
      // Ajouter des contacts par défaut si aucun contact n'existe
      if (_contacts.isEmpty) {
        await _addDefaultContacts();
      }
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('ContactService: Initialisé avec ${_contacts.length} contacts');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ContactService: Erreur initialisation: $e');
      }
    }
  }

  /// Charge les contacts depuis le stockage local
  Future<void> _loadContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactsJson = prefs.getString(_contactsKey);
      
      if (contactsJson != null) {
        final List<dynamic> contactsList = json.decode(contactsJson);
        _contacts = contactsList.map((json) => Contact.fromJson(json)).toList();
        
        if (kDebugMode) {
          print('ContactService: ${_contacts.length} contacts chargés');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('ContactService: Erreur chargement contacts: $e');
      }
    }
  }

  /// Sauvegarde les contacts dans le stockage local
  Future<void> _saveContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactsJson = json.encode(_contacts.map((c) => c.toJson()).toList());
      await prefs.setString(_contactsKey, contactsJson);
      
      if (kDebugMode) {
        print('ContactService: ${_contacts.length} contacts sauvegardés');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ContactService: Erreur sauvegarde contacts: $e');
      }
    }
  }

  /// Ajoute des contacts par défaut
  Future<void> _addDefaultContacts() async {
    final now = DateTime.now();
    
    final defaultContacts = [
      Contact(
        id: 'emergency_police',
        name: 'Police',
        phone: '17',
        type: ContactType.emergency,
        isEmergency: true,
        createdAt: now,
        updatedAt: now,
      ),
      Contact(
        id: 'emergency_samu',
        name: 'SAMU',
        phone: '15',
        type: ContactType.emergency,
        isEmergency: true,
        createdAt: now,
        updatedAt: now,
      ),
      Contact(
        id: 'emergency_pompiers',
        name: 'Pompiers',
        phone: '18',
        type: ContactType.emergency,
        isEmergency: true,
        createdAt: now,
        updatedAt: now,
      ),
      Contact(
        id: 'dr_martin',
        name: 'Dr. Martin',
        phone: '+225 01 23 45 67',
        email: '<EMAIL>',
        type: ContactType.medical,
        isFavorite: true,
        lastCallDate: now.subtract(const Duration(hours: 2)),
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now,
      ),
      Contact(
        id: 'famille_kouassi',
        name: 'Famille Kouassi',
        phone: '+225 07 89 12 34',
        type: ContactType.family,
        isFavorite: true,
        lastMessageDate: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now,
      ),
      Contact(
        id: 'centre_medical',
        name: 'Centre médical',
        phone: '+225 05 67 89 01',
        type: ContactType.medical,
        lastCallDate: now.subtract(const Duration(days: 3)),
        createdAt: now.subtract(const Duration(days: 90)),
        updatedAt: now,
      ),
      Contact(
        id: 'pharmacie_coin',
        name: 'Pharmacie du coin',
        phone: '+225 02 34 56 78',
        type: ContactType.pharmacy,
        lastMessageDate: now.subtract(const Duration(days: 3)),
        createdAt: now.subtract(const Duration(days: 45)),
        updatedAt: now,
      ),
      Contact(
        id: 'taxi_abidjan',
        name: 'Taxi Abidjan',
        phone: '+225 08 90 12 34',
        type: ContactType.transport,
        lastCallDate: now.subtract(const Duration(days: 14)),
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now,
      ),
    ];

    for (final contact in defaultContacts) {
      await addContact(contact);
    }
  }

  /// Ajoute un nouveau contact
  Future<Contact> addContact(Contact contact) async {
    final newContact = contact.copyWith(
      id: contact.id.isEmpty ? _generateId() : contact.id,
      updatedAt: DateTime.now(),
    );
    
    _contacts.add(newContact);
    await _saveContacts();
    
    if (kDebugMode) {
      print('ContactService: Contact ajouté: ${newContact.name}');
    }
    
    return newContact;
  }

  /// Met à jour un contact existant
  Future<Contact?> updateContact(Contact contact) async {
    final index = _contacts.indexWhere((c) => c.id == contact.id);
    if (index == -1) return null;
    
    final updatedContact = contact.copyWith(updatedAt: DateTime.now());
    _contacts[index] = updatedContact;
    await _saveContacts();
    
    if (kDebugMode) {
      print('ContactService: Contact mis à jour: ${updatedContact.name}');
    }
    
    return updatedContact;
  }

  /// Supprime un contact
  Future<bool> deleteContact(String contactId) async {
    final index = _contacts.indexWhere((c) => c.id == contactId);
    if (index == -1) return false;
    
    final contact = _contacts.removeAt(index);
    await _saveContacts();
    
    if (kDebugMode) {
      print('ContactService: Contact supprimé: ${contact.name}');
    }
    
    return true;
  }

  /// Trouve un contact par ID
  Contact? getContactById(String id) {
    try {
      return _contacts.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Recherche des contacts par nom ou téléphone
  List<Contact> searchContacts(String query) {
    if (query.isEmpty) return contacts;
    
    final lowerQuery = query.toLowerCase();
    return _contacts.where((contact) {
      return contact.name.toLowerCase().contains(lowerQuery) ||
             contact.phone.contains(query) ||
             (contact.email?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// Met à jour la date du dernier appel
  Future<void> updateLastCall(String contactId) async {
    final contact = getContactById(contactId);
    if (contact != null) {
      await updateContact(contact.copyWith(lastCallDate: DateTime.now()));
    }
  }

  /// Met à jour la date du dernier message
  Future<void> updateLastMessage(String contactId) async {
    final contact = getContactById(contactId);
    if (contact != null) {
      await updateContact(contact.copyWith(lastMessageDate: DateTime.now()));
    }
  }

  /// Génère un ID unique
  String _generateId() {
    return 'contact_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Nettoie les ressources
  Future<void> dispose() async {
    _contacts.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('ContactService: Ressources libérées');
    }
  }
}
