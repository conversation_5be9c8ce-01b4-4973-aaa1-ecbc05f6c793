import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceService {
  static final VoiceService _instance = VoiceService._internal();
  factory VoiceService() => _instance;
  VoiceService._internal();

  final SpeechToText _speechToText = SpeechToText();
  final AudioRecorder _audioRecorder = AudioRecorder();
  
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isRecording = false;
  String _transcribedText = '';
  String? _currentAudioPath;
  
  StreamController<String>? _transcriptionController;
  StreamController<bool>? _listeningController;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  bool get isRecording => _isRecording;
  String get transcribedText => _transcribedText;
  String? get currentAudioPath => _currentAudioPath;

  // Streams
  Stream<String> get transcriptionStream => _transcriptionController?.stream ?? const Stream.empty();
  Stream<bool> get listeningStream => _listeningController?.stream ?? const Stream.empty();

  /// Initialise le service vocal
  Future<bool> initialize() async {
    try {
      // Demander les permissions
      final micPermission = await Permission.microphone.request();
      if (micPermission != PermissionStatus.granted) {
        if (kDebugMode) {
          print('VoiceService: Permission microphone refusée');
        }
        return false;
      }

      // Initialiser Speech-to-Text
      final speechAvailable = await _speechToText.initialize(
        onError: (error) {
          if (kDebugMode) {
            print('VoiceService: Erreur Speech-to-Text: $error');
          }
        },
        onStatus: (status) {
          if (kDebugMode) {
            print('VoiceService: Statut Speech-to-Text: $status');
          }
        },
      );

      if (!speechAvailable) {
        if (kDebugMode) {
          print('VoiceService: Speech-to-Text non disponible');
        }
        return false;
      }

      // Initialiser les contrôleurs de stream
      _transcriptionController = StreamController<String>.broadcast();
      _listeningController = StreamController<bool>.broadcast();

      _isInitialized = true;
      
      if (kDebugMode) {
        print('VoiceService: Initialisé avec succès');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('VoiceService: Erreur initialisation: $e');
      }
      return false;
    }
  }

  /// Démarre l'enregistrement vocal avec transcription en temps réel
  Future<bool> startVoiceRecording() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('VoiceService: Service non initialisé');
      }
      return false;
    }

    try {
      // Créer le chemin pour l'enregistrement audio
      final directory = await getApplicationDocumentsDirectory();
      final sosAudioDir = Directory('${directory.path}/sos_audio');
      if (!await sosAudioDir.exists()) {
        await sosAudioDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentAudioPath = '${sosAudioDir.path}/sos_audio_$timestamp.m4a';

      // Démarrer l'enregistrement audio
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _currentAudioPath!,
      );
      _isRecording = true;

      // Démarrer la transcription en temps réel
      await _speechToText.listen(
        onResult: (result) {
          _transcribedText = result.recognizedWords;
          _transcriptionController?.add(_transcribedText);
          
          if (kDebugMode) {
            print('VoiceService: Transcription: $_transcribedText');
          }
        },
        listenFor: const Duration(minutes: 2), // Maximum 2 minutes
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'fr_FR', // Français
        listenMode: ListenMode.confirmation,
      );

      _isListening = true;
      _listeningController?.add(true);

      if (kDebugMode) {
        print('VoiceService: Enregistrement vocal démarré');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('VoiceService: Erreur démarrage enregistrement: $e');
      }
      return false;
    }
  }

  /// Arrête l'enregistrement vocal
  Future<VoiceRecordingResult?> stopVoiceRecording() async {
    try {
      String? audioPath;
      String finalText = _transcribedText;

      // Arrêter l'enregistrement audio
      if (_isRecording) {
        audioPath = await _audioRecorder.stop();
        _isRecording = false;
      }

      // Arrêter la transcription
      if (_isListening) {
        await _speechToText.stop();
        _isListening = false;
        _listeningController?.add(false);
      }

      if (kDebugMode) {
        print('VoiceService: Enregistrement arrêté');
        print('VoiceService: Fichier audio: $audioPath');
        print('VoiceService: Transcription finale: $finalText');
      }

      return VoiceRecordingResult(
        audioPath: audioPath ?? _currentAudioPath,
        transcribedText: finalText,
        duration: Duration.zero, // TODO: Calculer la durée réelle
      );
    } catch (e) {
      if (kDebugMode) {
        print('VoiceService: Erreur arrêt enregistrement: $e');
      }
      return null;
    }
  }

  /// Nettoie les anciens fichiers audio
  Future<void> cleanupOldAudioFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final sosAudioDir = Directory('${directory.path}/sos_audio');
      
      if (await sosAudioDir.exists()) {
        final files = sosAudioDir.listSync();
        final now = DateTime.now();
        
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);
            
            // Supprimer les fichiers de plus de 7 jours
            if (age.inDays > 7) {
              await file.delete();
              if (kDebugMode) {
                print('VoiceService: Fichier supprimé: ${file.path}');
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('VoiceService: Erreur nettoyage: $e');
      }
    }
  }

  /// Libère les ressources
  Future<void> dispose() async {
    try {
      if (_isListening) {
        await _speechToText.stop();
      }
      if (_isRecording) {
        await _audioRecorder.stop();
      }
      
      await _transcriptionController?.close();
      await _listeningController?.close();
      
      _isInitialized = false;
      _isListening = false;
      _isRecording = false;
      _transcribedText = '';
      _currentAudioPath = null;
      
      if (kDebugMode) {
        print('VoiceService: Ressources libérées');
      }
    } catch (e) {
      if (kDebugMode) {
        print('VoiceService: Erreur libération ressources: $e');
      }
    }
  }
}

/// Résultat d'un enregistrement vocal
class VoiceRecordingResult {
  final String? audioPath;
  final String transcribedText;
  final Duration duration;

  VoiceRecordingResult({
    required this.audioPath,
    required this.transcribedText,
    required this.duration,
  });

  bool get hasAudio => audioPath != null && audioPath!.isNotEmpty;
  bool get hasText => transcribedText.isNotEmpty;
  bool get isValid => hasAudio || hasText;

  @override
  String toString() {
    return 'VoiceRecordingResult(audioPath: $audioPath, transcribedText: "$transcribedText", duration: $duration)';
  }
}
