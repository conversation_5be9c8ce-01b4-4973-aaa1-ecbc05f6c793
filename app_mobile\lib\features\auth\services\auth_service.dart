import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // URL de base de votre API Node.js
  static const String _baseUrl = 'http://192.168.252.247:3000/api';

  User? _currentUser;
  String? _token;
  bool _isInitialized = false;

  User? get currentUser => _currentUser;
  String? get token => _token;
  bool get isLoggedIn => _currentUser != null && _token != null;
  bool get isInitialized => _isInitialized;

  /// Initialise le service d'authentification
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      _token = prefs.getString('auth_token');
      final userJson = prefs.getString('current_user');

      if (_token != null && userJson != null) {
        try {
          final userMap = json.decode(userJson);
          _currentUser = User.fromJson(userMap);

          // Vérifier le token en arrière-plan (sans bloquer l'initialisation)
          _validateTokenInBackground();
        } catch (parseError) {
          if (kDebugMode) {
            print('AuthService: Erreur parsing utilisateur: $parseError');
          }
          // Nettoyer les données corrompues
          await logout();
        }
      }

      _isInitialized = true;
      
      if (kDebugMode) {
        print('AuthService: Initialisé - Utilisateur connecté: ${_currentUser?.fullName ?? 'Aucun'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthService: Erreur initialisation: $e');
      }
      await logout(); // Nettoyer en cas d'erreur
      _isInitialized = true;
    }
  }

  /// Connexion utilisateur
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      if (kDebugMode) {
        print('AuthService: Tentative de connexion pour $email');
        print('AuthService: URL: $_baseUrl/auth/login');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'email_utilisateur': email,
          'mot_de_passe': password,
        }),
      ).timeout(const Duration(seconds: 30));

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        _token = data['token'];
        _currentUser = User.fromJson(data['user']);

        // Sauvegarder en local
        await _saveAuthData();

        if (kDebugMode) {
          print('AuthService: Connexion réussie pour ${_currentUser!.fullName}');
        }

        return {
          'success': true,
          'user': _currentUser,
          'message': 'Connexion réussie',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur de connexion',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthService: Erreur connexion: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion. Vérifiez votre connexion internet.',
      };
    }
  }

  /// Inscription utilisateur
  Future<Map<String, dynamic>> register({
    required String nomUtilisateur,
    required String prenomUtilisateur,
    required String emailUtilisateur,
    required String motDePasse,
    String? contactUtilisateur,
    String? adresseUtilisateur,
  }) async {
    try {
      if (kDebugMode) {
        print('AuthService: Tentative d\'inscription pour $emailUtilisateur');
        print('AuthService: URL: $_baseUrl/auth/register');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/register'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'nom_utilisateur': nomUtilisateur,
          'prenom_utilisateur': prenomUtilisateur,
          'email_utilisateur': emailUtilisateur,
          'mot_de_passe': motDePasse,
          'contact_utilisateur': contactUtilisateur,
          'adresse_utilisateur': adresseUtilisateur,
        }),
      ).timeout(const Duration(seconds: 30));

      final data = json.decode(response.body);

      if (response.statusCode == 201) {
        _token = data['token'];
        _currentUser = User.fromJson(data['user']);

        // Sauvegarder en local
        await _saveAuthData();

        if (kDebugMode) {
          print('AuthService: Inscription réussie pour ${_currentUser!.fullName}');
        }

        return {
          'success': true,
          'user': _currentUser,
          'message': 'Compte créé avec succès',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la création du compte',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthService: Erreur inscription: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion. Vérifiez votre connexion internet.',
      };
    }
  }

  /// Déconnexion
  Future<void> logout() async {
    try {
      if (_token != null) {
        // Informer le serveur de la déconnexion
        await http.post(
          Uri.parse('$_baseUrl/auth/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('AuthService: Erreur déconnexion serveur: $e');
      }
    }

    // Nettoyer les données locales
    _currentUser = null;
    _token = null;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('current_user');

    if (kDebugMode) {
      print('AuthService: Déconnexion effectuée');
    }
  }

  /// Nettoie complètement le stockage local (pour résoudre les problèmes de format)
  Future<void> clearAllData() async {
    _currentUser = null;
    _token = null;
    _isInitialized = false;

    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    if (kDebugMode) {
      print('AuthService: Toutes les données locales supprimées');
    }
  }

  /// Valider le token en arrière-plan (sans bloquer l'initialisation)
  void _validateTokenInBackground() {
    Future.delayed(Duration.zero, () async {
      try {
        final isValid = await _validateToken();
        if (!isValid) {
          await logout();
          if (kDebugMode) {
            print('AuthService: Token expiré, utilisateur déconnecté');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('AuthService: Erreur validation token en arrière-plan: $e');
        }
      }
    });
  }

  /// Valider le token actuel
  Future<bool> _validateToken() async {
    if (_token == null) return false;

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/auth/validate'),
        headers: {
          'Authorization': 'Bearer $_token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      if (kDebugMode) {
        print('AuthService: Erreur validation token: $e');
      }
      return false;
    }
  }

  /// Sauvegarder les données d'authentification
  Future<void> _saveAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    
    if (_token != null) {
      await prefs.setString('auth_token', _token!);
    }
    
    if (_currentUser != null) {
      await prefs.setString('current_user', json.encode(_currentUser!.toJson()));
    }
  }

  /// Obtenir les headers avec authentification
  Map<String, String> getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      if (_token != null) 'Authorization': 'Bearer $_token',
    };
  }

  /// Vérifier si l'utilisateur a accès à une fonctionnalité
  bool hasAccess(String feature) {
    // Tous les utilisateurs ont accès à toutes les fonctionnalités
    // Vous pouvez personnaliser cette logique selon vos besoins
    return _currentUser != null;
  }
}
