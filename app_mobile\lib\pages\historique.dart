import 'package:flutter/material.dart';

class HistoriquePage extends StatelessWidget {
  const HistoriquePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre
              const Text(
                'Historique',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 20),

              // Statistiques du jour
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7900),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFF7900).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Text(
                      'Aujourd\'hui',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('Obstacles', '15', Icons.warning),
                        _buildStatItem('Distance', '2.3 km', Icons.directions_walk),
                        _buildStatItem('Temps', '45 min', Icons.access_time),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Historique des activités
              const Text(
                'Activités récentes',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16),

              Expanded(
                child: ListView(
                  children: [
                    _buildHistoryItem(
                      'Obstacle détecté',
                      'Poteau électrique sur le trottoir',
                      '14:30',
                      Icons.warning,
                      Colors.orange,
                    ),
                    _buildHistoryItem(
                      'Trajet terminé',
                      'Pharmacie du coin → Domicile (800m)',
                      '14:15',
                      Icons.check_circle,
                      Colors.green,
                    ),
                    _buildHistoryItem(
                      'Appel d\'urgence',
                      'Contact: Dr. Martin',
                      '13:45',
                      Icons.phone,
                      Colors.blue,
                    ),
                    _buildHistoryItem(
                      'Obstacle évité',
                      'Travaux sur la chaussée',
                      '13:20',
                      Icons.construction,
                      Colors.orange,
                    ),
                    _buildHistoryItem(
                      'Message reçu',
                      'Rappel de rendez-vous médical',
                      '12:30',
                      Icons.message,
                      Colors.purple,
                    ),
                    _buildHistoryItem(
                      'Trajet commencé',
                      'Domicile → Pharmacie du coin',
                      '12:00',
                      Icons.directions_walk,
                      Colors.green,
                    ),
                    _buildHistoryItem(
                      'Obstacle détecté',
                      'Véhicule mal garé',
                      '11:45',
                      Icons.warning,
                      Colors.orange,
                    ),
                    _buildHistoryItem(
                      'Canne connectée',
                      'Connexion Bluetooth établie',
                      '08:00',
                      Icons.bluetooth_connected,
                      Colors.blue,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryItem(String title, String description, String time, IconData icon, Color iconColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      time,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
