class Message {
  final String id;
  final String contactId;
  final String content;
  final MessageType type;
  final MessageStatus status;
  final bool isFromMe;
  final DateTime sentAt;
  final DateTime? deliveredAt;
  final DateTime? readAt;
  final String? attachmentPath;
  final String? attachmentType;
  final bool isEmergency;

  Message({
    required this.id,
    required this.contactId,
    required this.content,
    required this.type,
    required this.status,
    required this.isFromMe,
    required this.sentAt,
    this.deliveredAt,
    this.readAt,
    this.attachmentPath,
    this.attachmentType,
    this.isEmergency = false,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] ?? '',
      contactId: json['contact_id'] ?? '',
      content: json['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${json['type']}',
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == 'MessageStatus.${json['status']}',
        orElse: () => MessageStatus.sent,
      ),
      isFromMe: json['is_from_me'] ?? false,
      sentAt: DateTime.parse(json['sent_at']),
      deliveredAt: json['delivered_at'] != null 
          ? DateTime.parse(json['delivered_at']) 
          : null,
      readAt: json['read_at'] != null 
          ? DateTime.parse(json['read_at']) 
          : null,
      attachmentPath: json['attachment_path'],
      attachmentType: json['attachment_type'],
      isEmergency: json['is_emergency'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'contact_id': contactId,
      'content': content,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'is_from_me': isFromMe,
      'sent_at': sentAt.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'attachment_path': attachmentPath,
      'attachment_type': attachmentType,
      'is_emergency': isEmergency,
    };
  }

  Message copyWith({
    String? id,
    String? contactId,
    String? content,
    MessageType? type,
    MessageStatus? status,
    bool? isFromMe,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    String? attachmentPath,
    String? attachmentType,
    bool? isEmergency,
  }) {
    return Message(
      id: id ?? this.id,
      contactId: contactId ?? this.contactId,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      isFromMe: isFromMe ?? this.isFromMe,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      attachmentType: attachmentType ?? this.attachmentType,
      isEmergency: isEmergency ?? this.isEmergency,
    );
  }

  String get timeText {
    final now = DateTime.now();
    final difference = now.difference(sentAt);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}min';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}j';
    } else {
      return '${sentAt.day}/${sentAt.month}';
    }
  }

  String get fullTimeText {
    final now = DateTime.now();
    final isToday = now.day == sentAt.day && 
                   now.month == sentAt.month && 
                   now.year == sentAt.year;
    
    if (isToday) {
      return '${sentAt.hour.toString().padLeft(2, '0')}:${sentAt.minute.toString().padLeft(2, '0')}';
    } else {
      return '${sentAt.day}/${sentAt.month} ${sentAt.hour.toString().padLeft(2, '0')}:${sentAt.minute.toString().padLeft(2, '0')}';
    }
  }

  bool get isRead => readAt != null;
  bool get isDelivered => deliveredAt != null;
  bool get hasAttachment => attachmentPath != null && attachmentPath!.isNotEmpty;

  String get statusIcon {
    if (isFromMe) {
      switch (status) {
        case MessageStatus.sending:
          return '⏳';
        case MessageStatus.sent:
          return '✓';
        case MessageStatus.delivered:
          return '✓✓';
        case MessageStatus.read:
          return '✓✓';
        case MessageStatus.failed:
          return '❌';
      }
    }
    return '';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Message && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Message{id: $id, contactId: $contactId, content: $content, type: $type, isFromMe: $isFromMe}';
  }
}

enum MessageType {
  text,
  voice,
  image,
  location,
  emergency,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

extension MessageTypeExtension on MessageType {
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Texte';
      case MessageType.voice:
        return 'Vocal';
      case MessageType.image:
        return 'Image';
      case MessageType.location:
        return 'Position';
      case MessageType.emergency:
        return 'Urgence';
    }
  }

  String get icon {
    switch (this) {
      case MessageType.text:
        return '💬';
      case MessageType.voice:
        return '🎤';
      case MessageType.image:
        return '📷';
      case MessageType.location:
        return '📍';
      case MessageType.emergency:
        return '🚨';
    }
  }
}

class Conversation {
  final String contactId;
  final List<Message> messages;
  final DateTime lastActivity;
  final int unreadCount;

  Conversation({
    required this.contactId,
    required this.messages,
    required this.lastActivity,
    this.unreadCount = 0,
  });

  Message? get lastMessage => messages.isNotEmpty ? messages.last : null;
  
  bool get hasUnreadMessages => unreadCount > 0;

  String get lastMessagePreview {
    if (lastMessage == null) return 'Aucun message';
    
    final message = lastMessage!;
    if (message.type == MessageType.voice) {
      return '🎤 Message vocal';
    } else if (message.type == MessageType.image) {
      return '📷 Image';
    } else if (message.type == MessageType.location) {
      return '📍 Position partagée';
    } else if (message.type == MessageType.emergency) {
      return '🚨 Message d\'urgence';
    }
    
    return message.content;
  }
}
