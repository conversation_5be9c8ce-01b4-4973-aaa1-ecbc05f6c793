import 'package:flutter/material.dart';
import '../services/integrated_data_service.dart';
import '../models/contact.dart';
import '../models/message.dart';

class MessagesPage extends StatefulWidget {
  const MessagesPage({super.key});

  @override
  State<MessagesPage> createState() => _MessagesPageState();
}

class _MessagesPageState extends State<MessagesPage> {
  final IntegratedDataService _integratedService = IntegratedDataService();

  bool _isLoading = true;
  bool _hasPermissions = false;
  List<Conversation> _conversations = [];
  List<Contact> _emergencyContacts = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _integratedService.initialize();

    // Vérifier les permissions
    final permissions = await _integratedService.getPermissionsStatus();
    _hasPermissions = permissions['sms'] == true;

    // Charger les données
    final allContacts = await _integratedService.getAllContacts();

    setState(() {
      _conversations = _integratedService.messageService.conversations;
      _emergencyContacts = allContacts.where((c) => c.isEmergency).toList();
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        body: Center(
          child: CircularProgressIndicator(
            color: Color(0xFFFF7900),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text(
          'Messages',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_hasPermissions)
            IconButton(
              onPressed: () => _requestPermissions(),
              icon: const Icon(Icons.security),
              tooltip: 'Demander permissions SMS',
            ),
          IconButton(
            onPressed: () => _showEmergencyMessageDialog(),
            icon: const Icon(Icons.emergency),
            tooltip: 'Message d\'urgence',
          ),
          IconButton(
            onPressed: () => _showPermissionsDialog(),
            icon: const Icon(Icons.info_outline),
            tooltip: 'Statut permissions',
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Message d'urgence rapide
              GestureDetector(
                onTap: () => _showEmergencyMessageDialog(),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Row(
                    children: [
                      Icon(
                        Icons.emergency,
                        color: Colors.white,
                        size: 32,
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Message d\'urgence rapide',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.send,
                        color: Colors.white,
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // Barre de recherche
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const TextField(
                  decoration: InputDecoration(
                    hintText: 'Rechercher une conversation...',
                    border: InputBorder.none,
                    icon: Icon(Icons.search, color: Color(0xFFFF7900)),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Liste des conversations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Conversations récentes',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    '${_conversations.length} conversation${_conversations.length > 1 ? 's' : ''}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              Expanded(
                child: _conversations.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.message_outlined,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Aucune conversation',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView(
                        children: _conversations.map((conversation) {
                          final contact = _integratedService.contactService.getContactById(conversation.contactId);
                          return _buildConversationItem(conversation, contact);
                        }).toList(),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConversationItem(Conversation conversation, Contact? contact) {
    final displayName = contact?.name ?? 'Contact inconnu';
    final displayPhone = contact?.formattedPhone ?? '';
    final lastMessage = conversation.lastMessage;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _openConversation(conversation, contact),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Stack(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF7900).withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getContactIcon(contact?.type ?? ContactType.personal),
                        color: const Color(0xFFFF7900),
                        size: 24,
                      ),
                    ),
                    if (conversation.hasUnreadMessages)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '${conversation.unreadCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              displayName,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: conversation.hasUnreadMessages
                                    ? FontWeight.bold
                                    : FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Text(
                            lastMessage?.timeText ?? '',
                            style: TextStyle(
                              fontSize: 12,
                              color: conversation.hasUnreadMessages
                                  ? const Color(0xFFFF7900)
                                  : Colors.grey,
                              fontWeight: conversation.hasUnreadMessages
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                      if (displayPhone.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          displayPhone,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (lastMessage?.isFromMe == true) ...[
                            Text(
                              lastMessage?.statusIcon ?? '',
                              style: const TextStyle(fontSize: 12),
                            ),
                            const SizedBox(width: 4),
                          ],
                          if (lastMessage?.type == MessageType.emergency)
                            const Icon(
                              Icons.emergency,
                              color: Colors.red,
                              size: 14,
                            ),
                          if (lastMessage?.type == MessageType.voice)
                            const Icon(
                              Icons.mic,
                              color: Colors.grey,
                              size: 14,
                            ),
                          if (lastMessage?.type == MessageType.image)
                            const Icon(
                              Icons.image,
                              color: Colors.grey,
                              size: 14,
                            ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              conversation.lastMessagePreview,
                              style: TextStyle(
                                fontSize: 14,
                                color: conversation.hasUnreadMessages
                                    ? Colors.black87
                                    : Colors.grey,
                                fontWeight: conversation.hasUnreadMessages
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getContactIcon(ContactType type) {
    switch (type) {
      case ContactType.emergency:
        return Icons.emergency;
      case ContactType.medical:
        return Icons.local_hospital;
      case ContactType.family:
        return Icons.family_restroom;
      case ContactType.pharmacy:
        return Icons.local_pharmacy;
      case ContactType.transport:
        return Icons.local_taxi;
      case ContactType.service:
        return Icons.support_agent;
      default:
        return Icons.person;
    }
  }

  void _openConversation(Conversation conversation, Contact? contact) {
    // Marquer les messages comme lus
    _integratedService.messageService.markMessagesAsRead(conversation.contactId);

    // Actualiser l'affichage
    _initializeServices();

    // Ici vous pourriez naviguer vers une page de conversation détaillée
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ouverture conversation avec ${contact?.name ?? 'Contact inconnu'}'),
        backgroundColor: const Color(0xFFFF7900),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showEmergencyMessageDialog() {
    if (_emergencyContacts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Aucun contact d\'urgence configuré'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.emergency, color: Colors.red),
              SizedBox(width: 8),
              Text('Message d\'urgence'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Sélectionnez un contact d\'urgence:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 200,
                child: ListView(
                  children: _emergencyContacts.map((contact) {
                    return ListTile(
                      leading: Icon(
                        _getContactIcon(contact.type),
                        color: Colors.red,
                      ),
                      title: Text(contact.name),
                      subtitle: Text(contact.formattedPhone),
                      onTap: () {
                        Navigator.of(context).pop();
                        _sendEmergencyMessage(contact);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _sendEmergencyMessage(Contact contact) async {
    final messageController = TextEditingController(
      text: 'J\'ai besoin d\'aide immédiatement. Merci de me contacter.',
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Message d\'urgence à ${contact.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: messageController,
                decoration: const InputDecoration(
                  labelText: 'Message d\'urgence',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                final success = await _integratedService.sendSMS(
                  contact.phone,
                  '🚨 URGENCE: ${messageController.text}',
                );

                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Message d\'urgence envoyé à ${contact.name}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );

                  _initializeServices();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Envoyer'),
            ),
          ],
        );
      },
    );
  }

  void _showNewMessageDialog(BuildContext context) async {
    final messageController = TextEditingController();
    Contact? selectedContact;
    final allContacts = await _integratedService.getAllContacts();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Nouveau message'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<Contact>(
                    value: selectedContact,
                    decoration: const InputDecoration(
                      labelText: 'Destinataire',
                      border: OutlineInputBorder(),
                    ),
                    items: allContacts.map((contact) {
                      return DropdownMenuItem(
                        value: contact,
                        child: Row(
                          children: [
                            Icon(
                              _getContactIcon(contact.type),
                              size: 16,
                              color: const Color(0xFFFF7900),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                contact.name,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (contact) {
                      setState(() {
                        selectedContact = contact;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: messageController,
                    decoration: const InputDecoration(
                      labelText: 'Message',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: selectedContact != null && messageController.text.isNotEmpty
                      ? () async {
                          Navigator.of(context).pop();

                          final message = Message(
                            id: '',
                            contactId: selectedContact!.id,
                            content: messageController.text,
                            type: MessageType.text,
                            status: MessageStatus.sending,
                            isFromMe: true,
                            sentAt: DateTime.now(),
                          );

                          await _integratedService.sendSMS(selectedContact!.phone, messageController.text);

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Message envoyé à ${selectedContact!.name}'),
                                backgroundColor: const Color(0xFFFF7900),
                              ),
                            );

                            _initializeServices();
                          }
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF7900),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Envoyer'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _requestPermissions() async {
    final success = await _integratedService.requestPermissions();

    if (success) {
      setState(() {
        _hasPermissions = true;
      });

      // Recharger les données avec les nouvelles permissions
      _initializeServices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions accordées ! Accès aux SMS de l\'appareil activé.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions refusées. Fonctionnalités limitées.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showPermissionsDialog() async {
    final permissions = await _integratedService.getPermissionsStatus();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.blue),
            SizedBox(width: 8),
            Text('Statut des permissions'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Permissions nécessaires pour accéder aux données de l\'appareil:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            _buildPermissionItem('Téléphone', permissions['phone'] ?? false),
            _buildPermissionItem('SMS', permissions['sms'] ?? false),
            _buildPermissionItem('Contacts', permissions['contacts'] ?? false),
            const SizedBox(height: 16),
            Text(
              _integratedService.isDeviceAccessEnabled
                  ? '✅ Accès aux données de l\'appareil activé'
                  : '❌ Accès aux données de l\'appareil désactivé',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: _integratedService.isDeviceAccessEnabled
                    ? Colors.green
                    : Colors.red,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Avec les permissions, vous pouvez:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text('• Voir vos vrais SMS'),
            const Text('• Envoyer des SMS natifs'),
            const Text('• Accéder à vos contacts'),
            const Text('• Voir l\'historique des appels'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (!_integratedService.isDeviceAccessEnabled)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _requestPermissions();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Demander'),
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(String name, bool granted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.cancel,
            color: granted ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(name),
        ],
      ),
    );
  }
}
