/// Enregistrement d'un appel
class CallRecord {
  final String id;
  final String phoneNumber;
  final String? contactId;
  final String contactName;
  final CallType type;
  final DateTime timestamp;
  final Duration duration;
  final bool isEmergency;

  CallRecord({
    required this.id,
    required this.phoneNumber,
    this.contactId,
    required this.contactName,
    required this.type,
    required this.timestamp,
    required this.duration,
    this.isEmergency = false,
  });

  factory CallRecord.fromJson(Map<String, dynamic> json) {
    return CallRecord(
      id: json['id'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      contactId: json['contact_id'],
      contactName: json['contact_name'] ?? '',
      type: CallType.values.firstWhere(
        (e) => e.toString() == 'CallType.${json['type']}',
        orElse: () => CallType.outgoing,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      duration: Duration(seconds: json['duration_seconds'] ?? 0),
      isEmergency: json['is_emergency'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone_number': phoneNumber,
      'contact_id': contactId,
      'contact_name': contactName,
      'type': type.toString().split('.').last,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'duration_seconds': duration.inSeconds,
      'is_emergency': isEmergency,
    };
  }

  CallRecord copyWith({
    String? id,
    String? phoneNumber,
    String? contactId,
    String? contactName,
    CallType? type,
    DateTime? timestamp,
    Duration? duration,
    bool? isEmergency,
  }) {
    return CallRecord(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      contactId: contactId ?? this.contactId,
      contactName: contactName ?? this.contactName,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      duration: duration ?? this.duration,
      isEmergency: isEmergency ?? this.isEmergency,
    );
  }

  String get formattedDuration {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds.remainder(60)}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}j';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'maintenant';
    }
  }

  String get timeText {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inMinutes < 60) {
      return 'Il y a ${difference.inMinutes}min';
    } else if (difference.inHours < 24) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  @override
  String toString() {
    return 'CallRecord{id: $id, contactName: $contactName, type: $type, timestamp: $timestamp}';
  }
}

enum CallType {
  incoming,
  outgoing,
  missed,
  rejected,
}

extension CallTypeExtension on CallType {
  String get displayName {
    switch (this) {
      case CallType.incoming:
        return 'Entrant';
      case CallType.outgoing:
        return 'Sortant';
      case CallType.missed:
        return 'Manqué';
      case CallType.rejected:
        return 'Rejeté';
    }
  }

  String get icon {
    switch (this) {
      case CallType.incoming:
        return '📞';
      case CallType.outgoing:
        return '📱';
      case CallType.missed:
        return '📵';
      case CallType.rejected:
        return '🚫';
    }
  }
}
