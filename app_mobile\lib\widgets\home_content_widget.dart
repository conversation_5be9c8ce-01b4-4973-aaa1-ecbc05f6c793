import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';
import '../services/location_service.dart';
import '../services/mqtt_service.dart';
import '../services/journey_service.dart';
import 'map_widget.dart';

/// Widget principal du contenu de la homepage
class HomeContentWidget extends StatefulWidget {
  const HomeContentWidget({super.key});

  @override
  State<HomeContentWidget> createState() => _HomeContentWidgetState();
}

class _HomeContentWidgetState extends State<HomeContentWidget> {
  final LocationService _locationService = LocationService();
  final MqttService _mqttService = MqttService();
  final JourneyService _journeyService = JourneyService();

  String _positionText = 'En attente de position...';
  String _locationName = 'Recherche du lieu...';
  LatLng? _currentPosition;
  List<LatLng> _trace = [];
  double _totalDistance = 0.0;
  bool _isTracking = true;
  StreamSubscription? _positionSubscription;

  static const double _minDistance = 5.0;

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  Future<void> _initServices() async {
    try {
      // Initialiser le service de trajets
      await _journeyService.initialize();

      // Connecter MQTT (peut échouer sans affecter la géolocalisation)
      await _mqttService.connect();
    } catch (e) {
      print('HomeContentWidget: Erreur MQTT: $e');
    }

    try {
      // Démarrer le suivi GPS
      await _locationService.startTracking();

      _positionSubscription = _locationService.positionStream.listen(
        (position) async {
          final current = LatLng(position.latitude, position.longitude);

          // Publier sur MQTT si connecté
          try {
            _mqttService.publishGpsPosition(
              latitude: position.latitude,
              longitude: position.longitude,
              altitude: position.altitude,
              accuracy: position.accuracy,
              speed: position.speed,
              heading: position.heading,
            );
          } catch (e) {
            print('HomeContentWidget: Erreur publication MQTT: $e');
          }

          // Obtenir le nom du lieu
          _updateLocationName(position.latitude, position.longitude);

          // Ajouter le point au trajet actuel si en cours
          if (_journeyService.currentJourney != null) {
            await _journeyService.addPointToJourney(
              current,
              locationName: _locationName,
              speed: position.speed,
              accuracy: position.accuracy,
            );
          }

          if (mounted) {
            setState(() {
              _positionText = _locationService.formatPosition(position);
              _currentPosition = current;
              _addPointToTrace(current);
            });
          }
        },
        onError: (error) {
          print('HomeContentWidget: Erreur géolocalisation: $error');
          if (mounted) {
            setState(() {
              _positionText = 'Erreur de géolocalisation';
              _locationName = 'Vérifiez les permissions GPS';
            });
          }
        },
      );
    } catch (e) {
      print('HomeContentWidget: Erreur initialisation GPS: $e');
      if (mounted) {
        setState(() {
          _positionText = 'GPS non disponible';
          _locationName = 'Activez la géolocalisation';
        });
      }
    }
  }

  Future<void> _updateLocationName(double latitude, double longitude) async {
    final locationName = await _locationService.getLocationName(latitude, longitude);
    if (mounted) {
      setState(() {
        _locationName = locationName;
      });
    }
  }

  void _addPointToTrace(LatLng newPoint) {
    if (!_isTracking) return;

    if (_trace.isEmpty) {
      _trace.add(newPoint);
      return;
    }

    final lastPoint = _trace.last;
    final distance = _locationService.calculateDistance(lastPoint, newPoint);

    if (distance >= _minDistance) {
      _trace.add(newPoint);
      _totalDistance += distance;
    }
  }

  void _toggleTracking() {
    setState(() => _isTracking = !_isTracking);
  }

  void _clearTrace() {
    setState(() {
      _trace.clear();
      _totalDistance = 0.0;
    });
  }

  Future<void> _requestLocationPermissions() async {
    try {
      bool hasPermission = await _locationService.checkLocationPermissions();
      if (hasPermission) {
        // Redémarrer les services
        await _initServices();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('GPS activé ! Localisation en cours...'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Permissions GPS refusées. Activez-les dans les paramètres.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('HomeContentWidget: Erreur demande permissions: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'activation du GPS'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    _locationService.stopTracking();
    _mqttService.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 18.0),
        child: Column(
          children: [
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF7900),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.all(
                          Radius.circular(16),
                        ),
                        child: GpsMapWidget(
                          currentPosition: _currentPosition,
                          trace: _trace,
                          totalDistance: _totalDistance,
                          isTracking: _isTracking,
                          onToggleTracking: _toggleTracking,
                          onClearTrace: _clearTrace,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            _buildLocationInfoCard(),
            const SizedBox(height: 20),
            Expanded(
              flex: 2,
              child: _buildJourneyInfoList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfoCard() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Votre Position Actuelle',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.orange,
                    width: 1,
                  ),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.orange,
                      size: 16,
                    ),
                    SizedBox(width: 6),
                    Text(
                      'Limité',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _locationName,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFFFF7900),
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            _currentPosition != null
                ? 'Lat: ${_currentPosition!.latitude.toStringAsFixed(6)}, Lon: ${_currentPosition!.longitude.toStringAsFixed(6)}'
                : 'Coordonnées en attente...',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          if (_currentPosition == null) ...[
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _requestLocationPermissions,
              icon: const Icon(Icons.location_on, size: 18),
              label: const Text('Activer GPS'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildJourneyInfoList() {
    final currentJourney = _journeyService.currentJourney;
    final lastJourney = _journeyService.lastJourney;
    final lastPlace = _journeyService.lastVisitedPlace;

    return ListView(
      children: [
        // Boutons d'action pour les trajets (toujours en premier)
        if (currentJourney == null)
          _buildActionItem(
            title: 'Démarrer un trajet',
            subtitle: 'Commencer l\'enregistrement de votre parcours',
            icon: Icons.play_arrow,
            color: Colors.green,
            onTap: _startJourney,
          )
        else
          _buildActionItem(
            title: 'Terminer le trajet',
            subtitle: 'Arrêter l\'enregistrement du parcours',
            icon: Icons.stop,
            color: Colors.red,
            onTap: _endJourney,
          ),

        // Trajet en cours
        if (currentJourney != null)
          _buildListItem(
            title: 'Trajet en cours',
            subtitle: 'Démarré ${currentJourney.formattedDuration} - ${currentJourney.formattedDistance}',
            icon: Icons.directions_walk,
            color: Colors.blue,
            onTap: () => _showJourneyDetails(currentJourney),
          ),

        // Dernier trajet
        if (lastJourney != null)
          _buildListItem(
            title: 'Dernier trajet',
            subtitle: '${lastJourney.formattedDistance} en ${lastJourney.formattedDuration}',
            icon: Icons.route,
            color: Colors.green,
            onTap: () => _showJourneyDetails(lastJourney),
          ),

        // Dernier lieu visité
        if (lastPlace != null)
          _buildListItem(
            title: 'Dernier lieu visité',
            subtitle: '${lastPlace.name} - ${lastPlace.formattedLastVisited}',
            icon: Icons.place,
            color: Colors.orange,
            onTap: () => _showPlaceDetails(lastPlace),
          ),
      ],
    );
  }

  Widget _buildListItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildActionItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _startJourney() async {
    if (_currentPosition != null) {
      await _journeyService.startJourney(_currentPosition!, locationName: _locationName);
      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Trajet démarré ! Votre parcours est maintenant enregistré.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _endJourney() async {
    await _journeyService.endCurrentJourney(endLocationName: _locationName);
    setState(() {});

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Trajet terminé et sauvegardé !'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showJourneyDetails(Journey journey) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(journey.isActive ? 'Trajet en cours' : 'Détails du trajet'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Distance', journey.formattedDistance),
            _buildDetailRow('Durée', journey.formattedDuration),
            _buildDetailRow('Début', journey.startLocationName ?? 'Lieu inconnu'),
            if (journey.endLocationName != null)
              _buildDetailRow('Fin', journey.endLocationName!),
            _buildDetailRow('Points enregistrés', '${journey.points.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showPlaceDetails(VisitedPlace place) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lieu visité'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Nom', place.name),
            _buildDetailRow('Dernière visite', place.formattedLastVisited),
            _buildDetailRow('Nombre de visites', '${place.visitCount}'),
            _buildDetailRow('Position',
              '${place.position.latitude.toStringAsFixed(4)}, ${place.position.longitude.toStringAsFixed(4)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
