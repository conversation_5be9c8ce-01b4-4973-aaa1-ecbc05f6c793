import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:latlong2/latlong.dart';

/// Service pour gérer la localisation GPS et le géocodage inverse
class LocationService {
  Stream<Position>? _positionStream;

  /// Stream des positions GPS
  Stream<Position> get positionStream {
    _positionStream ??= Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5,
      ),
    );
    return _positionStream!;
  }

  /// Démarre le suivi GPS
  Future<void> startTracking() async {
    // Vérifier les permissions avant de démarrer
    bool hasPermission = await checkLocationPermissions();
    if (!hasPermission) {
      print('LocationService: Permissions de localisation refusées');
      return;
    }

    print('LocationService: Suivi GPS démarré avec succès');
  }

  /// Arrête le suivi GPS
  void stopTracking() {
    _positionStream = null;
  }

  /// Formate une position en URL Google Maps
  String formatPosition(Position position) {
    return 'https://www.google.com/maps/search/?api=1&query=${position.latitude},${position.longitude}';
  }

  /// Vérifie les permissions de localisation
  Future<bool> checkLocationPermissions() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  /// Obtient le nom du lieu à partir des coordonnées
  Future<String> getLocationName(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String locationName = '';

        if (place.street != null && place.street!.isNotEmpty) {
          locationName += place.street!;
        }

        if (place.locality != null && place.locality!.isNotEmpty) {
          if (locationName.isNotEmpty) locationName += ', ';
          locationName += place.locality!;
        }

        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          if (locationName.isNotEmpty) locationName += ', ';
          locationName += place.administrativeArea!;
        }

        if (place.country != null && place.country!.isNotEmpty) {
          if (locationName.isNotEmpty) locationName += ', ';
          locationName += place.country!;
        }

        return locationName.isNotEmpty ? locationName : 'Lieu inconnu';
      }
      return 'Lieu inconnu';
    } catch (e) {
      print('Erreur lors de la récupération du nom du lieu: $e');
      return 'Impossible de déterminer le lieu';
    }
  }

  /// Calcule la distance entre deux points
  double calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    );
  }

  /// Obtient la position actuelle
  Future<Position?> getCurrentPosition() async {
    try {
      bool hasPermission = await checkLocationPermissions();
      if (!hasPermission) {
        return null;
      }
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      print('Erreur lors de l\'obtention de la position: $e');
      return null;
    }
  }
}
