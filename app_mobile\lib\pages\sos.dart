import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../services/sos_service.dart';
import '../services/camera_service.dart';
import '../services/voice_service.dart';
import '../widgets/sos_widget.dart';
import '../widgets/sos_camera_widget.dart';
import '../widgets/sos_history_widget.dart';

class SOSPage extends StatefulWidget {
  const SOSPage({super.key});

  @override
  State<SOSPage> createState() => _SOSPageState();
}

class _SOSPageState extends State<SOSPage> {
  final SOSService _sosService = SOSService();
  final CameraService _cameraService = CameraService.instance;
  final VoiceService _voiceService = VoiceService();
  Position? _currentPosition;
  bool _showCamera = false;
  bool _isSOSActive = false;
  bool _isVoiceSOSActive = false;
  bool _showHistory = false;
  String? _currentVideoPath;
  String? _currentAudioPath;
  String _transcribedText = '';
  List<String> _capturedPhotos = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialiser la caméra
      await _cameraService.initialize();

      // Initialiser le service vocal
      await _voiceService.initialize();

      // Obtenir la position GPS
      try {
        final position = await Geolocator.getCurrentPosition();
        if (mounted) {
          setState(() {
            _currentPosition = position;
          });
        }
      } catch (e) {
        print('Erreur GPS: $e');
      }

    } catch (e) {
      print('Erreur initialisation SOS: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text(
          'Urgence SOS',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.red,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: !_isSOSActive && !_isVoiceSOSActive && !_showHistory
            ? _buildMainSOSInterface()
            : _isVoiceSOSActive
                ? _buildVoiceSOSInterface()
                : _showHistory
                    ? _buildHistoryInterface()
                    : _buildActiveSOSInterface(),
      ),
    );
  }

  Widget _buildMainSOSInterface() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Informations importantes en haut
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange[700],
                  size: 24,
                ),
                const SizedBox(height: 8),
                Text(
                  'En cas d\'urgence réelle, contactez directement:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  '🚨 Police: 17 | 🚑 SAMU: 15 | 🚒 Pompiers: 18',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 40),

          // Boutons SOS
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Bouton SOS classique
              GestureDetector(
                onTap: _showSOSDialog,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning,
                        size: 50,
                        color: Colors.white,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'SOS',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1,
                        ),
                      ),
                      Text(
                        'VISUEL',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          letterSpacing: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Bouton SOS vocal
              GestureDetector(
                onTap: _startVoiceSOS,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.mic,
                        size: 50,
                        color: Colors.white,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'SOS',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1,
                        ),
                      ),
                      Text(
                        'VOCAL',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          letterSpacing: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 30),

          // Messages d'instruction
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                Text(
                  'Choisissez votre type d\'alerte SOS :',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '🔴 SOS VISUEL\nEnregistrement vidéo + photos',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '🟠 SOS VOCAL\nEnregistrement audio + transcription',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 30),

          // Bouton historique
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 40),
            child: ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _showHistory = true;
                });
              },
              icon: const Icon(Icons.history, size: 20),
              label: const Text(
                'Voir l\'historique des alertes',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: Colors.grey[700],
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                  side: BorderSide(color: Colors.grey[300]!),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveSOSInterface() {
    return Column(
      children: [
        // En-tête SOS actif
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              const Icon(
                Icons.warning,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(height: 8),
              const Text(
                'ALERTE SOS ACTIVE',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Enregistrement en cours...',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Caméra SOS
        Expanded(
          child: SOSCameraWidget(
            autoStartRecording: true,
            onVideoRecorded: (videoPath) {
              setState(() {
                _currentVideoPath = videoPath;
              });
            },
            onPhotoTaken: (photoPath) {
              if (photoPath != null) {
                setState(() {
                  _capturedPhotos.add(photoPath);
                });
              }
            },
            onCameraError: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('⚠️ Erreur caméra - SOS envoyé sans vidéo'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 20),

        // Boutons de contrôle
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              onPressed: _stopSOS,
              icon: const Icon(Icons.stop),
              label: const Text('Arrêter SOS'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _sendSOSWithMedia,
              icon: const Icon(Icons.send),
              label: const Text('Envoyer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }



  void _showSOSDialog() {
    showDialog(
      context: context,
      builder: (context) => SOSDialog(
        currentPosition: _currentPosition,
        onSOSTriggered: () {
          Navigator.of(context).pop();
          _activateSOS();
        },
      ),
    );
  }

  void _startVoiceSOS() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SOS Vocal'),
        content: const Text(
          'Voulez-vous déclencher une alerte SOS vocale ?\n\n'
          'Cette action va :\n'
          '• Démarrer l\'enregistrement audio\n'
          '• Transcrire votre message en temps réel\n'
          '• Capturer votre position GPS\n'
          '• Envoyer l\'alerte avec audio et texte',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _triggerVoiceSOS();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Démarrer SOS Vocal'),
          ),
        ],
      ),
    );
  }

  void _activateSOS() {
    setState(() {
      _isSOSActive = true;
      _showCamera = true;
      _currentVideoPath = null;
      _capturedPhotos.clear();
    });

    // Nettoyer les anciens fichiers
    _cameraService.cleanupOldFiles();
  }

  Future<void> _triggerVoiceSOS() async {
    try {
      setState(() {
        _isVoiceSOSActive = true;
        _transcribedText = '';
        _currentAudioPath = null;
      });

      // Nettoyer les anciens fichiers audio
      await _voiceService.cleanupOldAudioFiles();

      // Démarrer l'enregistrement vocal
      final started = await _voiceService.startVoiceRecording();
      if (!started) {
        throw Exception('Impossible de démarrer l\'enregistrement vocal');
      }

      // Écouter les transcriptions en temps réel
      _voiceService.transcriptionStream.listen((text) {
        if (mounted) {
          setState(() {
            _transcribedText = text;
          });
        }
      });

      // Attendre 30 secondes puis arrêter automatiquement
      await Future.delayed(const Duration(seconds: 30));

      if (_isVoiceSOSActive) {
        await _stopVoiceSOS();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur SOS vocal: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );

        setState(() {
          _isVoiceSOSActive = false;
        });
      }
    }
  }

  Future<void> _stopVoiceSOS() async {
    try {
      // Arrêter l'enregistrement vocal
      final result = await _voiceService.stopVoiceRecording();

      if (result != null && result.isValid) {
        setState(() {
          _currentAudioPath = result.audioPath;
          _transcribedText = result.transcribedText;
        });

        // Envoyer le SOS vocal
        await _sendVoiceSOSWithMedia(result);
      } else {
        throw Exception('Enregistrement vocal invalide');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur arrêt SOS vocal: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVoiceSOSActive = false;
        });
      }
    }
  }

  void _stopSOS() {
    setState(() {
      _isSOSActive = false;
      _showCamera = false;
    });

    // Arrêter la caméra
    _cameraService.stopCamera();
  }

  Future<void> _sendSOSWithMedia() async {
    try {
      // Arrêter l'enregistrement si en cours
      final videoPath = await _cameraService.stopRecording();
      if (videoPath != null) {
        _currentVideoPath = videoPath;
      }

      // Créer le message SOS avec les médias
      String message = 'Alerte SOS avec preuves visuelles';
      if (_currentVideoPath != null) {
        message += '\n📹 Vidéo: ${_currentVideoPath!.split('/').last}';
      }
      if (_capturedPhotos.isNotEmpty) {
        message += '\n📸 Photos: ${_capturedPhotos.length} fichier(s)';
      }

      // Envoyer l'alerte SOS
      final alert = await _sosService.triggerSOS(
        type: SOSType.autre,
        message: message,
        urgencyLevel: UrgencyLevel.critique,
        customPosition: _currentPosition,
        videoPath: _currentVideoPath,
        photoPaths: _capturedPhotos,
      );

      if (mounted) {
        if (alert.status == SOSStatus.sent) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Alerte SOS envoyée avec succès !'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ Alerte envoyée partiellement'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }

        // Arrêter le mode SOS
        _stopSOS();

        // Actualiser les services
        _initializeServices();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Échec de l\'envoi: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _sendVoiceSOSWithMedia(VoiceRecordingResult result) async {
    try {
      // Créer le message SOS avec les données vocales
      String message = 'Alerte SOS vocale';
      if (result.hasText && result.transcribedText.isNotEmpty) {
        message = result.transcribedText;
      }

      // Envoyer l'alerte SOS avec audio et transcription
      final alert = await _sosService.triggerSOS(
        type: SOSType.autre,
        message: message,
        urgencyLevel: UrgencyLevel.critique,
        customPosition: _currentPosition,
        audioPath: result.audioPath,
        transcribedText: result.transcribedText,
      );

      if (mounted) {
        if (alert.status == SOSStatus.sent) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Alerte SOS vocale envoyée avec succès !'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ Alerte vocale envoyée partiellement'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }

        // Actualiser les services
        _initializeServices();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Échec de l\'envoi vocal: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Widget _buildVoiceSOSInterface() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // En-tête SOS vocal actif
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.mic,
                  color: Colors.white,
                  size: 48,
                ),
                const SizedBox(height: 12),
                const Text(
                  'SOS VOCAL ACTIF',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Enregistrement audio en cours...',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 30),

          // Animation microphone
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.mic,
              size: 60,
              color: Colors.orange,
            ),
          ),

          const SizedBox(height: 30),

          // Transcription en temps réel
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Transcription en temps réel :',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _transcribedText.isEmpty
                      ? 'Parlez maintenant...'
                      : _transcribedText,
                  style: TextStyle(
                    fontSize: 16,
                    color: _transcribedText.isEmpty
                        ? Colors.grey[500]
                        : Colors.black87,
                    fontStyle: _transcribedText.isEmpty
                        ? FontStyle.italic
                        : FontStyle.normal,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 40),

          // Bouton arrêter
          ElevatedButton(
            onPressed: _stopVoiceSOS,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.stop, size: 24),
                SizedBox(width: 8),
                Text(
                  'Arrêter et Envoyer',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Instructions
          Text(
            'Parlez clairement pour décrire votre urgence.\n'
            'L\'enregistrement s\'arrêtera automatiquement dans 30 secondes.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryInterface() {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Historique des alertes SOS',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            setState(() {
              _showHistory = false;
            });
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Forcer le rechargement de l'historique
              setState(() {
                _showHistory = false;
              });
              Future.delayed(const Duration(milliseconds: 100), () {
                setState(() {
                  _showHistory = true;
                });
              });
            },
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Statistiques rapides
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFFF7900).withOpacity(0.1),
                    const Color(0xFFFF7900).withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFFF7900).withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Icon(
                          Icons.warning,
                          color: Colors.red[600],
                          size: 24,
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Critiques',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const Text(
                          '0', // TODO: Calculer dynamiquement
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Icon(
                          Icons.priority_high,
                          color: Colors.orange[600],
                          size: 24,
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Urgentes',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const Text(
                          '0', // TODO: Calculer dynamiquement
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Icon(
                          Icons.info,
                          color: Colors.blue[600],
                          size: 24,
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Modérées',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const Text(
                          '0', // TODO: Calculer dynamiquement
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Icon(
                          Icons.history,
                          color: Colors.grey[600],
                          size: 24,
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Total',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const Text(
                          '0', // TODO: Calculer dynamiquement
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Liste des alertes
            Expanded(
              child: SOSHistoryWidget(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            _showHistory = false;
          });
        },
        backgroundColor: const Color(0xFFFF7900),
        foregroundColor: Colors.white,
        child: const Icon(Icons.add_alert),
        tooltip: 'Nouvelle alerte SOS',
      ),
    );
  }
}
